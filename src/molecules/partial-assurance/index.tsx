import { Input } from 'reactstrap';
import './styles.scss';
import classNames from 'classnames';

interface Props {
  isAssured?: boolean;
  classes?: Record<string, string>;
  checked?: boolean;
  handleChange?: (value: boolean) => void;
}

export const PartialAssurance = ({
  isAssured = false,
  classes,
  checked,
  handleChange = () => {},
}: Props) => {
  if (isAssured) {
    return (
      <div className={classes?.wrapper ?? 'partial-assurance-wrapper'}>
        <i className='fal fa-award text-ThemeSuccessMedium fs-4'></i>
      </div>
    );
  }

  return (
    <div className={classes?.wrapper ?? 'partial-assurance-wrapper'}>
      <Input
        type='checkbox'
        className={classNames('m-0 partial-assurance-checkbox', classes?.input)}
        checked={checked}
        onChange={(e) => handleChange(e.currentTarget.checked)}
      />
    </div>
  );
};
