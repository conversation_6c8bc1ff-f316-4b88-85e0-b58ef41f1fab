import './styles.scss';
import type { SurveyModelMinimalUtrv } from '@models/surveyData';
import type { UtrvAssuranceStatus } from '@g17eco/types/universalTrackerValue';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';

interface AssuranceStatusBadgeProps {
  utrv?: SurveyModelMinimalUtrv;
}

const assuranceStatusColumns: Partial<Record<UtrvAssuranceStatus, { className: string; text: string }>> = {
  created: {
    className: 'text-ThemeIconSecondary',
    text: 'Assurance pending',
  },
  rejected: {
    className: 'text-ThemeDangerDark',
    text: 'Assurance disputed',
  },
  completed: {
    className: 'text-ThemeSuccessMedium',
    text: 'Assured',
  },
  restated: {
    className: 'text-ThemeSuccessMedium',
    text: 'Restated',
  },
  completed_open: {
    className: 'text-ThemeSuccessMedium',
    text: 'Open for modifications',
  },
  partial: {
    className: 'partially-assured-icon',
    text: 'Partially assured',
  },
};

export const AssuranceStatusBadge = ({ utrv }: AssuranceStatusBadgeProps) => {
  if (!utrv || !utrv.assuranceStatus) {
    return null;
  }

  return (
    <div className='badge__wrapper d-inline-block'>
      <SimpleTooltip text={assuranceStatusColumns[utrv.assuranceStatus]?.text}>
        <i className={`fal fa-2x fa-award ${assuranceStatusColumns[utrv.assuranceStatus]?.className}`} />
      </SimpleTooltip>
    </div>
  );
};
