.badge__wrapper {
  .badge {
    font-size: 0.8rem;
    text-transform: uppercase;
    line-height: 1.25rem;
    font-weight: 700;
  }

  .question__privacy-badge {
    color: var(--theme-AccentDark);
    background-color: var(--theme-BgExtralight) !important;
    border: 1px solid var(--theme-AccentDark);
  }

  .question__status-badge {
    text-align: center;
    background-color: var(--theme-ColourWhite) !important;
    color: var(--theme-AccentDark);
    border: 1px solid var(--theme-AccentDark);
  }

  .status-info {
    &.verified {
      color: var(--theme-BgExtralight);
      background-color: var(--theme-SuccessMedium) !important;
    }

    &.created {
      color: var(--theme-TextMedium);
      background-color: var(--theme-NeutralsLight) !important;
    }

    &.rejected {
      color: var(--theme-BgExtralight);
      background-color: var(--theme-DangerDark) !important;
    }

    &.updated {
      color: var(--theme-HeadingMedium);
      border: 1px solid var(--theme-HeadingMedium);
      background-color: var(--theme-BgExtralight) !important;
    }
  }

  .na-nr-info {
    .na-nr-info-top {
      color: var(--theme-TextMedium);
      border: 1px solid var(--theme-TextMedium);
      background-color: var(--theme-TextWhite) !important;
    }
  }
}
