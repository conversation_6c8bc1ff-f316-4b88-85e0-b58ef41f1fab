import { vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { Configuration } from './Configuration';
import userEvent from '@testing-library/user-event';
import { createWorkgroup } from '@fixtures/workgroup-fixtures';

const mockPush = vi.fn();
vi.mock('react-router-dom', () => ({
  useHistory: () => ({
    push: mockPush,
  }),
  withRouter: (component: any) => component,
}));

vi.mock('@api/workgroups', () => ({
  useCreateWorkgroupMutation: () => [vi.fn(), { isLoading: false }],
  useUpdateWorkgroupMutation: () => [vi.fn(), { isLoading: false }],
}));

describe('Configuration component', () => {
  const initiativeId = 'initiative-id';
  const workgroup = createWorkgroup({ name: 'Existing Workgroup' });
  const workgroup2 = createWorkgroup({ _id: 'wg-2', name: 'Another Workgroup' });
  const workgroups = [workgroup, workgroup2];

  describe('Unique workgroup name', () => {
    it('should show warning when workgroup name already exists when creating', async () => {
      render(<Configuration initiativeId={initiativeId} workgroups={workgroups} />);

      const input = screen.getByLabelText('Workgroup name*');
      await userEvent.type(input, workgroup.name);

      expect(screen.getByText('Workgroup name already exists, please choose a new name')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Save' })).toBeDisabled();
    });

    it('should show warning when editing a workgroup to an existing name', async () => {
      render(<Configuration initiativeId={initiativeId} workgroups={workgroups} workgroup={workgroup2} />);

      const input = screen.getByLabelText('Workgroup name*');
      await userEvent.clear(input);
      await userEvent.type(input, 'Existing Workgroup');

      expect(screen.getByText('Workgroup name already exists, please choose a new name')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Save' })).toBeDisabled();
    });

    it('should not show warning for the same workgroup name when editing', async () => {
      render(<Configuration initiativeId={initiativeId} workgroups={workgroups} workgroup={workgroup} />);

      const input = screen.getByLabelText('Workgroup name*');
      await userEvent.clear(input);
      await userEvent.type(input, 'Existing Workgroup');

      expect(screen.queryByText('Workgroup name already exists, please choose a new name')).not.toBeInTheDocument();
    });

    it('should enable save button when name is unique', async () => {
      render(<Configuration initiativeId={initiativeId} workgroups={workgroups} />);

      const input = screen.getByLabelText('Workgroup name*');
      await userEvent.type(input, 'New Workgroup');

      expect(screen.queryByText('Workgroup name already exists, please choose a new name')).not.toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Save' })).not.toBeDisabled();
    });
  });
});
