import './style.scss';
import { Navigation, NavTab } from '@components/company-settings/Navigation';
import { DashboardSection, DashboardSectionTitle } from '@g17eco/molecules/dashboard';
import { useTabs } from '@hooks/useTabs';
import { Configuration } from './Configuration';
import { useGetWorkgroupQuery, useGetWorkgroupsQuery } from '@api/workgroups';
import { skipToken } from '@reduxjs/toolkit/query';
import { Loader } from '@g17eco/atoms/loader';
import { ManageUsers } from './ManageUsers';
import { getEditingUrl, ManageWorkgroupTab } from './utils';
import { useHistory } from 'react-router-dom';

const getTabs = (isCreating: boolean): NavTab[] => [
  {
    code: ManageWorkgroupTab.Configuration,
    label: 'Configuration',
  },
  {
    code: ManageWorkgroupTab.Users,
    label: 'Manage workgroup users',
    disabled: isCreating,
  },
];

interface Props {
  workgroupId?: string;
  initiativeId: string;
  tab?: ManageWorkgroupTab;
}

export const CreateOrEditWorkgroup = ({ workgroupId, initiativeId, tab }: Props) => {
  const history = useHistory();
  const { currentTab, handleChangeTab } = useTabs({
    initialTab: tab ?? ManageWorkgroupTab.Configuration,
    changeTabCallback: (tab) => workgroupId && history.push(getEditingUrl(initiativeId, workgroupId, tab)),
  });

  const { data: workgroup, isFetching } = useGetWorkgroupQuery(
    workgroupId
      ? {
          initiativeId,
          workgroupId,
        }
      : skipToken,
  );

  const { data: workgroups } = useGetWorkgroupsQuery(initiativeId);

  return (
    <>
      <DashboardSectionTitle title={`${workgroupId ? 'Edit' : 'Create'} workgroup`} />
      <DashboardSection>
        <Navigation
          navTabs={getTabs(!workgroupId)}
          page={currentTab}
          onClick={(tab) => handleChangeTab(tab as ManageWorkgroupTab)}
        />
        {isFetching ? <Loader /> : null}
        {currentTab === ManageWorkgroupTab.Configuration ? (
          <Configuration
            key={workgroup?._id}
            workgroup={workgroup}
            initiativeId={initiativeId}
            workgroups={workgroups ?? []}
          />
        ) : null}
        {currentTab === ManageWorkgroupTab.Users && workgroup ? (
          <ManageUsers key={workgroup._id} workgroup={workgroup} initiativeId={initiativeId} />
        ) : null}
      </DashboardSection>
    </>
  );
};
