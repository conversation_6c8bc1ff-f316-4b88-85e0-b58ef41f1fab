import { ROUTES } from '@constants/routes';
import { generateUrl, generateUrlWithQuery } from '@routes/util';

export enum ManageWorkgroupTab {
  Configuration = 'configuration',
  Users = 'users',
}

export const isManageWorkgroupTab = (tab: string | null | undefined): tab is ManageWorkgroupTab =>
  Object.values(ManageWorkgroupTab).includes(tab as ManageWorkgroupTab);

export const getCreatingUrl = (initiativeId: string) =>
  `${generateUrl(ROUTES.MANAGE_WORKGROUPS, { initiativeId })}/create`;

export const getEditingUrl = (
  initiativeId: string,
  workgroupId: string,
  tab: ManageWorkgroupTab = ManageWorkgroupTab.Configuration,
) => generateUrlWithQuery(ROUTES.MANAGE_WORKGROUPS, { initiativeId, workgroupId }, { tab });