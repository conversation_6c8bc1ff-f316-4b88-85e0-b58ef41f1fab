/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { useCallback, useMemo, useState } from 'react';
import { ScopeQuestionGroupOptionalValue } from '../../types/survey';
import { cloneDeep } from '@utils/object';
import { ScrollToTop } from '../button/ScrollToTop';
import { SurveyGroupList } from './SurveyGroupList';
import { GroupQuestionListColumn, GroupQuestionListColumnProps, GroupQuestionListProps, HeaderProps } from './types';
import { ColumnCode } from '../survey-question-list/partials/ColumnCode';
import { ColumnTitle } from '@g17eco/molecules/survey-question-list';
import { ColumnCheckbox } from './ColumnCheckbox';
import { GroupCheckbox } from './GroupCheckbox';
import { UserDelegationData } from '../../api/admin-dashboard';
import './SurveyGroupContainer.scss';
import '../survey-question-list/survey-question-list.scss';
import { SearchDebounce } from '../survey/survey-question-list-toolbar/partials/SearchQuestions';
import { Col } from 'reactstrap';
import { BaseScopeQuestion } from '../../types/surveyScope';
import { disableBySearch } from '../survey/utils/getDisableUtrs';
import { Document } from 'flexsearch';
import { QUESTION } from '@constants/terminology';

enum UtrRoleKey {
  Stakeholder = 'stakeholderUtrIds',
  Verifier = 'verifierUtrIds',
}

type AssignUtrs = Pick<UserDelegationData, 'stakeholderUtrIds' | 'verifierUtrIds'>;

export type SetAssignedUtrsFn = React.Dispatch<React.SetStateAction<UserDelegationData>>

export interface CombinedUtrsListProps extends AssignUtrs {
  initiativeId: string;
  surveyGroups: ScopeQuestionGroupOptionalValue[];
  handleNoRows?: () => void;
  noRowsMessage?: JSX.Element | string;
  alternativeCode?: string;
  headerColumns?: GroupQuestionListColumn[];
  columns?: GroupQuestionListColumn[];
  setAssignedUtrsFn: SetAssignedUtrsFn;
  searchIndex: Document<unknown>;
}

export const SurveyGroupContainer = (props: CombinedUtrsListProps) => {
  const { surveyGroups, setAssignedUtrsFn, stakeholderUtrIds, verifierUtrIds, searchIndex } = props;

  const [questionGroupDisplay, setQuestionGroupDisplay] = useState<string[]>([]);
  const [searchText, setSearch] = useState<string>();

  const disabledUTRs = useMemo(() => disableBySearch({surveyGroups, searchIndex, searchText}), [surveyGroups, searchIndex, searchText])

  const toggleClickQuestions = (type: keyof AssignUtrs, utrIds: string[]) => {
    setAssignedUtrsFn((prev) => {
      const isAllSelected = utrIds.every((utrId) => prev[type].some((id) => id === utrId));
      const uniqueSet = new Set<string>(prev[type]);

      if (isAllSelected) {
        // If everything selected, then this is an Unselect action
        utrIds.forEach((utrId) => uniqueSet.delete(utrId));
        return { ...prev, [type]: Array.from(uniqueSet) };
      }

      // If partially selected, then this is a Select-All action
      utrIds.forEach((utrId) => uniqueSet.add(utrId));
      return { ...prev, [type]: Array.from(uniqueSet) };
    });
  };

  const toggleQuestionGroup = (id: string) => {
    setQuestionGroupDisplay((prev) => {
      const newDisplay = [...prev];
      const index = newDisplay.indexOf(id);
      if (index >= 0) {
        newDisplay.splice(index, 1);
      } else {
        newDisplay.push(id);
      }
      return newDisplay;
    });
  };

  const isHidden = useCallback(
    (question: BaseScopeQuestion) => {
      if (disabledUTRs.length === 0) {
        return false;
      }
      return disabledUTRs.includes(question.universalTracker.getId());
    },
    [disabledUTRs]
  );

  const filteredSurveyGroups = useMemo(() => {
    const groups = cloneDeep(surveyGroups);
    let questionsCount = 0;
    for (let i = groups.length - 1; i >= 0; i--) {
      const group = groups[i];
      const isGroupHeading = !group.list.length;
      if (isGroupHeading) {
        group.count = questionsCount;
        questionsCount = 0;
      }

      const questionList = group.list.filter((q) => !isHidden(q));
      group.subGroups?.forEach(subGroup => {
        subGroup.list = subGroup.list.filter((q) => !isHidden(q));
      })

      group.list = questionList;
      questionsCount += questionList.length;
    }
    return groups;
  }, [surveyGroups, isHidden]);

  const ContributorCheckbox = (props: GroupQuestionListColumnProps) => {
    const { question } = props;
    const handleContributorClick = () => {
      return toggleClickQuestions(UtrRoleKey.Stakeholder, [question.universalTracker.getId()]);
    }
    const isContributor = stakeholderUtrIds.some(id => id === question.universalTracker.getId());
    return <ColumnCheckbox isSelected={isContributor} handleClick={handleContributorClick} question={question} />;
  }

  const VerifierCheckbox = (props: GroupQuestionListColumnProps) => {
    const { question } = props;
    const handleVerifierClick = () => {
      return toggleClickQuestions(UtrRoleKey.Verifier, [question.universalTracker.getId()]);
    }
    const isVerifier = verifierUtrIds.some(id => id === question.universalTracker.getId());
    return <ColumnCheckbox isSelected={isVerifier} handleClick={handleVerifierClick} question={question} />;
  }

  const ContributorGroupCheckbox = (props: GroupQuestionListProps) => {
    return (
      <GroupCheckbox
        {...props}
        selectedQuestionIds={stakeholderUtrIds}
        toggleClickQuestions={(utrIds: string[]) => {
          return toggleClickQuestions(UtrRoleKey.Stakeholder, utrIds);
        }}
      />
    );
  };

  const VerifierGroupCheckbox = (props: GroupQuestionListProps) => {
    return (
      <GroupCheckbox
        {...props}
        selectedQuestionIds={verifierUtrIds}
        toggleClickQuestions={(utrIds: string[]) => {
          return toggleClickQuestions(UtrRoleKey.Verifier, utrIds);
        }}
      />
    );
  };

  const columns = [
    {
      header: (props: HeaderProps) => (
        <div className='g17-th codeCol strong'>
          <span {...props}>
            Type code
          </span>
        </div>
      ),
      component: ColumnCode,
    },
    {
      header: (props: HeaderProps) => (
        <div className='g17-th flex-fill questionCol strong'>
          <span {...props}>
            {QUESTION.CAPITALIZED_SINGULAR}
          </span>
        </div>
      ),
      component: ColumnTitle,
    },
    {
      header: (props: HeaderProps) => (
        <div className='g17-th checkboxCol strong'>
          <span {...props}>
            Contributor
          </span>
        </div>
      ),
      component: ContributorCheckbox,
    },
    {
      header: (props: HeaderProps) => (
        <div className='g17-th checkboxCol strong'>
          <span {...props}>
            Verifier
          </span>
        </div>
      ),
      component: VerifierCheckbox,
    },
  ];
  const headerColumns = [ContributorGroupCheckbox, VerifierGroupCheckbox];

  return (
    <div className={'survey-group-container'}>
      <Col className='col-md-4 col-12 mb-3'>
        <SearchDebounce handleChange={(value: string) => setSearch(value)} placeholder={`Search for ${QUESTION.SINGULAR}...`} />
      </Col>
      <SurveyGroupList
        {...props}
        questionGroupDisplay={questionGroupDisplay}
        toggleQuestionGroup={toggleQuestionGroup}
        surveyGroups={filteredSurveyGroups}
        columns={columns}
        headerColumns={headerColumns}
      />
      <div className='combined-utrs-list__sticky-toolbar'>
        <ScrollToTop />
      </div>
    </div>
  );
};
