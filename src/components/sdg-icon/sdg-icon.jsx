/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { PureComponent } from 'react';

import { getSrc } from './utils';

class SDGIcon extends PureComponent {

  static defaultProps = {
    withText: false
  }

  render() {
    const { code, className, onClick, alt, profile, width, height, outline, maxWidth } = this.props;
    const noSize = !width && !height;
    return (
      <img
        className={className}
        style={{ maxWidth: maxWidth ?? 'unset' }}
        onClick={onClick}
        alt={alt}
        src={getSrc({ profile, code, outline, withText: this.props.withText })}
        width={noSize ? 20 : width}
        height={noSize ? 20 : height}
      />
    );
  }

}

export default SDGIcon;
