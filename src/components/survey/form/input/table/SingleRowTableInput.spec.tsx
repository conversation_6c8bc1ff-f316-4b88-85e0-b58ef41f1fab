/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { fireEvent } from '@testing-library/react'
import { InputColumn, TableInputProps } from './InputInterface';
import { DisplayCheckBox, TableDataInfo, RowStatus } from '../../../question/questionInterfaces';
import { TableColumnType, UniversalTrackerPlain, UtrValueType } from '../../../../../types/universalTracker';
import SingleRowTableInput from './SingleRowTableInput';
import UniversalTracker from '../../../../../model/UniversalTracker';
import { HandleValueChangeProps } from '../InputProps';
import { renderWithProviders } from '../../../../../__fixtures__/utils';
import { reduxFixtureStore } from '../../../../../__fixtures__/redux-store';
import { TableInputProvider } from './TableInputContext';
import { AIAssistantContextProvider } from '../../../../../features/assistant/ai-assistant/AIAssistantContext';

// change input value one at a time and query again the input elements to get the re-rendered visibility
// to test the internal component rerender logic
export const setInputFields = (container: HTMLElement, enteredColumns: { code: string, value?: string }[], index: number = 0) => {
  // query rendered fields to get latest visible inputs
  const inputFields = container.querySelectorAll('[name]')

  if (inputFields.length === 0 || index >= inputFields.length) {
    return
  }
  const code = inputFields[index].getAttribute('name') ?? ''

  //const inputCol= dp.tableData.rows[0].data.find(el => el.code === code)
  const inputEntered = enteredColumns.find(el => el.code === code)
  const value = inputEntered?.value ?? ''
  // change input value which triggers recalculating visibility in component
  fireEvent.change(inputFields[index], { target: { value: value } })

  // do again for next input field
  setInputFields(container, enteredColumns, index + 1)
}

export const getVisibleColumns = (container: HTMLElement,) => {
  const visibleColumnCodes: string[] = []
  container.querySelectorAll('[name]').forEach(input => {
    const name = input.getAttribute('name')
    if (!name) {
      return
    }
    visibleColumnCodes.push(name)
  })
  return visibleColumnCodes
}

const baseUtr = {
  type: 'kpi',
  name: undefined,
  valueLabel: undefined,
  created: new Date(),
  valueListOrdered: undefined,
  valueListTargets: undefined,
  targetDirection: undefined,
} as unknown as UniversalTrackerPlain;

const tableUtr: UniversalTrackerPlain = {
  ...baseUtr,
  _id: 'TableId',
  code: 'utr/code/c',
  valueType: UtrValueType.Table,
  valueValidation: {
    table: {
      validation: {
        maxRows: 1
      },
      columns: [
        {
          code: 'col1',
          name: 'Col 1',
          type: TableColumnType.Text,
          shortName: '',
        },
        {
          code: 'col2',
          name: 'Col 2',
          type: TableColumnType.Text,

          visibilityRule: {
            formula: 'resolveString(\'{col1}\', \'pass\')'
          },
          shortName: '',
        },
        {
          code: 'col3',
          name: 'Col 3',
          type: TableColumnType.Text,

          visibilityRule: {
            formula: 'resolveString(\'{col2}\', \'pass\')'
          },
          shortName: '',
        },
        {
          code: 'col4',
          name: 'Col 4',
          type: TableColumnType.Text,
          visibilityRule: {
            formula: 'resolveString(\'{col3}\', \'pass\')'
          },
          shortName: '',
        },
      ]
    }
  }
};
const nopFn = () => { }

const allVisible = {
  expected: ['col1', 'col2', 'col3', 'col4'],
  columnsEntered: [{ code: 'col1', value: 'pass' }, { code: 'col2', value: 'pass' }, { code: 'col3', value: 'pass' }, { code: 'col4', value: 'pass' }],
}

const allHidden = {
  expected: ['col1'],
  columnsEntered: [{ code: 'col1', value: 'NOT PASS' }, { code: 'col2' }, { code: 'col3' }, { code: 'col4' }],
}

const someVisible = {
  expected: ['col1', 'col2', 'col3'],
  columnsEntered: [{ code: 'col1', value: 'pass' }, { code: 'col2', value: 'pass' }, { code: 'col3', value: 'NOT PASS' }, { code: 'col4' }],
}

describe('<SingleRowTableInput /> not answered question state', () => {
  const dataProvider = [
    {
      testName: 'all hidden',
      utr: tableUtr,
      expected: allHidden.expected,
      enteredColumns: allHidden.columnsEntered,
      standardColumns: tableUtr.valueValidation?.table?.columns.filter(el => !el.visibilityRule && !el.calculation),
      visibilityRuleColumns: tableUtr.valueValidation?.table?.columns.filter(el => el.visibilityRule),
      calculationColumns: tableUtr.valueValidation?.table?.columns.filter(el => el.calculation),
      value: undefined,
      valueDataData: [],
      tableData: {
        editRowId: 0,
        rows: [
          {
            id: 0,
            rowStatus: RowStatus.original,
            isRemoved: false,
            isEdited: false,
            hasChanged: false,
            data: []
          }
        ]
      } as TableDataInfo
    },
    {
      testName: 'all visible',
      utr: tableUtr,
      expected: allVisible.expected,
      enteredColumns: allVisible.columnsEntered,
      standardColumns: tableUtr.valueValidation?.table?.columns.filter(el => !el.visibilityRule && !el.calculation),
      visibilityRuleColumns: tableUtr.valueValidation?.table?.columns.filter(el => el.visibilityRule),
      calculationColumns: tableUtr.valueValidation?.table?.columns.filter(el => el.calculation),
      value: undefined,
      valueDataData: [],
      tableData: {
        editRowId: 0,
        rows: [
          {
            id: 0,
            rowStatus: RowStatus.original,
            isRemoved: false,
            isEdited: false,
            hasChanged: false,
            data: []
          }
        ]
      } as TableDataInfo
    },
    {
      testName: 'Some visible',
      utr: tableUtr,
      expected: someVisible.expected,
      enteredColumns: someVisible.columnsEntered,
      standardColumns: tableUtr.valueValidation?.table?.columns.filter(el => !el.visibilityRule && !el.calculation),
      visibilityRuleColumns: tableUtr.valueValidation?.table?.columns.filter(el => el.visibilityRule),
      calculationColumns: tableUtr.valueValidation?.table?.columns.filter(el => el.calculation),
      value: undefined,
      valueDataData: [],
      tableData: {
        editRowId: 0,
        rows: [
          {
            id: 0,
            rowStatus: RowStatus.original,
            isRemoved: false,
            isEdited: false,
            hasChanged: false,
            data: []
          }
        ]
      } as TableDataInfo
    }
  ];

  dataProvider.forEach((dp, ix) => {
    it(`Run (${dp.testName})`, () => {
      const updatedColumns = new Map<string, InputColumn>()
      const props: TableInputProps = {
        universalTracker: new UniversalTracker(dp.utr),
        isDisabled: () => false,
        handleError: nopFn,
        handleCheckboxChange: nopFn,
        handleValueDataChange: (update: InputColumn[][]) => { console.log({ update }) },
        handleValueChange: ({ value, ...props }: HandleValueChangeProps) => { console.log({ value }) },
        handleUnitChange: nopFn,
        handleNumberScaleChange: nopFn,

        // Input values
        questionValue: dp.value,
        valueDataData: dp.valueDataData,

        // Multi-row table
        table: dp.tableData,
        // assert values are correct
        updateTable: (table: Partial<TableDataInfo>) => {
          table.rows?.forEach(row => row.data.forEach(col => updatedColumns.set(col.code, col)))
        },
        placeholder: 'Test Placeholder',
        saving: false,
        prefix: '',
        suffix: '',
        index: 1,
        unit: 'mt',
        numberScale: '',
        displayCheckbox: {} as DisplayCheckBox,
        scrollToRef: nopFn,
        calculationColumns: dp.calculationColumns ?? [],
        visibilityRuleColumns: dp.visibilityRuleColumns ?? [],
        status: 'updated',
        tableConfiguration: {
          columns: [...dp.standardColumns ?? [], ...dp.visibilityRuleColumns ?? [], ...dp.calculationColumns ?? []]
        },
      };

      const store = reduxFixtureStore();

      const view = renderWithProviders(
        <TableInputProvider>
          <AIAssistantContextProvider
            initiativeId='test-initiative'
            utrvId='test-utrv'
            utr={props.universalTracker.getRaw()}
          >
            <SingleRowTableInput {...props} />
          </AIAssistantContextProvider>
        </TableInputProvider>,
        {
          store,
        },
      );

      const container = view.baseElement

      setInputFields(container, dp.enteredColumns)

      const visibleColumnCodes = getVisibleColumns(container)

      // test visible columns rendered
      expect(visibleColumnCodes.sort()).toEqual(dp.expected.sort())
      // test column values updated
      dp.enteredColumns.forEach(expectedUpdate =>
        expect(updatedColumns.get(expectedUpdate.code)?.value).toEqual(expectedUpdate.value)
      )
    });
  });
});


