import { InputColumn } from '@components/survey/form/input/table/InputInterface';
import React, { createContext, useContext } from 'react';

interface ContextProps {
  formRow: InputColumn[];
  setFormRowData: React.Dispatch<React.SetStateAction<InputColumn[]>>;
}

const TableInputContext = createContext<ContextProps>({
  formRow: [],
  setFormRowData: () => {},
});

export const useTableInputContext = () => {
  return useContext(TableInputContext);
};

interface Props {
  children: React.ReactNode | React.ReactNode[];
}

export const TableInputProvider = ({ children }: Props) => {
  const [formRow, setFormRowData] = React.useState<InputColumn[]>([]);
  return <TableInputContext.Provider value={{ formRow, setFormRowData }}>{children}</TableInputContext.Provider>;
};
