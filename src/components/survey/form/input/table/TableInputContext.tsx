import { InputColumn } from '@components/survey/form/input/table/InputInterface';
import React, { createContext, useContext } from 'react';

interface ContextProps {
  formRow: InputColumn[];
  setFormRowData: React.Dispatch<React.SetStateAction<InputColumn[]>>;
}

const TableInputContext = createContext<ContextProps | null>(null);

export const useTableInputContext = () => {
  const context = useContext(TableInputContext);
  if (!context) {
    throw new Error('useTableInputContext must be used within a TableInputProvider');
  }
  return context;
};

interface Props {
  children: React.ReactNode | React.ReactNode[];
}

export const TableInputProvider = ({ children }: Props) => {
  const [formRow, setFormRowData] = React.useState<InputColumn[]>([]);
  return <TableInputContext.Provider value={{ formRow, setFormRowData }}>{children}</TableInputContext.Provider>;
};
