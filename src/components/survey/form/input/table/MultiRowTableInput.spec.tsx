/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { fireEvent, screen } from '@testing-library/react'
import { InputColumn, TableInputProps } from './InputInterface';
import { DisplayCheckBox, TableDataInfo, RowStatus } from '../../../question/questionInterfaces';
import { TableColumnType, UniversalTrackerPlain, UtrValueType } from '../../../../../types/universalTracker';
import UniversalTracker from '../../../../../model/UniversalTracker';
import MultiRowTableInput from './MultiRowTableInput';
import { getVisibleColumns, setInputFields } from './SingleRowTableInput.spec';
import userEvent from '@testing-library/user-event';
import { renderWithProviders } from '../../../../../__fixtures__/utils';
import { reduxFixtureStore } from '../../../../../__fixtures__/redux-store';
import { TableInputProvider } from './TableInputContext';
import { AIAssistantContextProvider } from '../../../../../features/assistant/ai-assistant/AIAssistantContext';


const baseUtr = {
  type: 'kpi',
  name: undefined,
  valueLabel: undefined,
  created: new Date(),
  valueListOrdered: undefined,
  valueListTargets: undefined,
  targetDirection: undefined,
} as unknown as UniversalTrackerPlain;

const tableUtrWithRequiredColumns: UniversalTrackerPlain = {
  ...baseUtr,
  _id: 'TableIdWithRequiredColumns',
  code: 'utr/code/d',
  valueType: UtrValueType.Table,
  valueValidation: {
    table: {
      columns: [
        {
          code: 'optionalCol',
          name: 'Optional Col',
          type: TableColumnType.Text,
        
        },
        {
          code: 'requiredCol1',
          name: 'Required Col 1',
          type: TableColumnType.Text,
          validation: {
            required: true,
          }
        },
        {
          code: 'requiredCol2',
          name: 'Required Col 2',
          type: TableColumnType.Number,
          validation: {
            required: true,
          }
        },
      ]
    }
  }
};

const tableUtr: UniversalTrackerPlain = {
  ...baseUtr,
  _id: 'TableId',
  code: 'utr/code/d',
  valueType: UtrValueType.Table,
  valueValidation: {
    table: {
      columns: [
        {
          code: 'col1',
          name: 'Col 1',
          type: TableColumnType.Text,
          shortName: '',
        },
        {
          code: 'col2',
          name: 'Col 2',
          type: TableColumnType.Text,

          visibilityRule: {
            formula: 'resolveString(\'{col1}\', \'pass\')'
          },
          shortName: '',
        },
        {
          code: 'col3',
          name: 'Col 3',
          type: TableColumnType.Text,

          visibilityRule: {
            formula: 'resolveString(\'{col2}\', \'pass\')'
          },
          shortName: '',
        },
        {
          code: 'col4',
          name: 'Col 4',
          type: TableColumnType.Text,
          visibilityRule: {
            formula: 'resolveString(\'{col3}\', \'pass\')'
          },
          shortName: '',
        },
      ]
    }
  }
};

const allVisible = {
  expected: ['col1', 'col2', 'col3', 'col4'],
  enteredColumns: [{ code: 'col1', value: 'pass' }, { code: 'col2', value: 'pass' }, { code: 'col3', value: 'pass' }, { code: 'col4', value: 'pass' }],
}

const allHidden = {
  expected: ['col1'],
  enteredColumns: [{ code: 'col1', value: 'NOT PASS' }, { code: 'col2' }, { code: 'col3' }, { code: 'col4' }],
}

const someVisible = {
  expected: ['col1', 'col2', 'col3'],
  enteredColumns: [{ code: 'col1', value: 'pass' }, { code: 'col2', value: 'pass' }, { code: 'col3', value: 'NOT PASS' }, { code: 'col4'}],
}



const nopFn = () => { }

describe('<MultiRowTableInput /> not answered question state', () => {
  const dataProvider = [
    {
      testName: 'all hidden',
      utr: tableUtr,
      expected: allHidden.expected,
      enteredColumns: allHidden.enteredColumns,
      standardColumns: tableUtr.valueValidation?.table?.columns.filter(el => !el.visibilityRule && !el.calculation),
      visibilityRuleColumns: tableUtr.valueValidation?.table?.columns.filter(el => el.visibilityRule),
      calculationColumns: tableUtr.valueValidation?.table?.columns.filter(el => el.calculation),
      value: undefined,
      valueDataData: [],
      tableData: {
        editRowId: -1,
        rows: [
          {
            id: 0,
            rowStatus: RowStatus.original,
            isRemoved: false,
            isEdited: false,
            hasChanged: false,
            data: []
          }
        ]
      } as TableDataInfo
    },
    {
      testName: 'all visible',
      utr: tableUtr,
      expected: allVisible.expected,
      enteredColumns: allVisible.enteredColumns,
      standardColumns: tableUtr.valueValidation?.table?.columns.filter(el => !el.visibilityRule && !el.calculation),
      visibilityRuleColumns: tableUtr.valueValidation?.table?.columns.filter(el => el.visibilityRule),
      calculationColumns: tableUtr.valueValidation?.table?.columns.filter(el => el.calculation),
      value: undefined,
      valueDataData: [],
      tableData: {
        editRowId: -1,
        rows: [
          {
            id: 0,
            rowStatus: RowStatus.original,
            isRemoved: false,
            isEdited: false,
            hasChanged: false,
            data: []
          }
        ]
      } as TableDataInfo
    },
    {
      testName: 'Some visible',
      utr: tableUtr,
      expected: someVisible.expected,
      enteredColumns: someVisible.enteredColumns,
      standardColumns: tableUtr.valueValidation?.table?.columns.filter(el => !el.visibilityRule && !el.calculation),
      visibilityRuleColumns: tableUtr.valueValidation?.table?.columns.filter(el => el.visibilityRule),
      calculationColumns: tableUtr.valueValidation?.table?.columns.filter(el => el.calculation),
      value: undefined,
      valueDataData: [],
      tableData: {
        editRowId: -1,
        rows: [
          {
            id: 0,
            rowStatus: RowStatus.original,
            isRemoved: false,
            isEdited: false,
            hasChanged: false,
            data: []
          }
        ]
      } as TableDataInfo
    }
  ];

  dataProvider.forEach((dp, ix) => {
    it(`Run (${dp.testName})`, () => {

      const tableData: TableDataInfo = {
        editRowId: 0,
        rows: [
          {
            id: 0,
            rowStatus: RowStatus.original,
            isRemoved: false,
            isEdited: false,
            hasChanged: false,
            data: []
          }
        ]
      }
      const updatedColumns = new Map<string, InputColumn>()

      const props: TableInputProps = {
        universalTracker: new UniversalTracker(dp.utr),
        isDisabled: () => false,
        handleError: nopFn,
        handleCheckboxChange: nopFn,
        handleValueDataChange: nopFn,
        handleValueChange: () => { },
        handleUnitChange: nopFn,
        handleNumberScaleChange: nopFn,
        status: 'updated',

        // Input values
        questionValue: dp.value,
        valueDataData: dp.valueDataData,

        // Multi-row table
        table: tableData,
        updateTable: (table: Partial<TableDataInfo>) => {
          table.rows?.forEach(row => row.data.forEach(col => updatedColumns.set(col.code, col)))
        },
        placeholder: 'Test Placeholder',
        saving: false,
        prefix: '',
        suffix: '',
        index: 1,
        unit: 'mt',
        numberScale: '',
        displayCheckbox: {} as DisplayCheckBox,
        scrollToRef: nopFn,
        calculationColumns: dp.calculationColumns ?? [],
        visibilityRuleColumns: dp.visibilityRuleColumns ?? [],
        tableConfiguration: {
          columns: [...dp.standardColumns ?? [], ...dp.visibilityRuleColumns ?? [], ...dp.calculationColumns ?? []]
        },
      };

      const store = reduxFixtureStore();

      const view = renderWithProviders(
        <TableInputProvider>
          <AIAssistantContextProvider
            initiativeId='test-initiative'
            utrvId='test-utrv'
            utr={props.universalTracker.getRaw()}
          >
            <MultiRowTableInput {...props} />
          </AIAssistantContextProvider>
        </TableInputProvider>,
        { store },
      );

      const container = view.baseElement

      setInputFields(container, dp.enteredColumns)
      // get visible columns rendered
      const visibileColumnCodes = getVisibleColumns(container)

      const btn = screen.getByText('Update row')
      fireEvent(btn, new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
      }))
      // test visible columns rendered
      expect(visibileColumnCodes.sort()).toEqual(dp.expected.sort())

      // test updated values
      dp.enteredColumns.forEach(expectedUpdate =>
        expect(updatedColumns.get(expectedUpdate.code)?.value).toEqual(expectedUpdate.value)
      );
    });
  });
});

describe('<MultiRowTableInput /> with required columns', () => {
  const getProps = ({ utr, tableData }: { utr: UniversalTrackerPlain; tableData: TableDataInfo }): TableInputProps => {
    return {
      universalTracker: new UniversalTracker(utr),
      isDisabled: () => false,
      handleError: nopFn,
      handleCheckboxChange: nopFn,
      handleValueDataChange: nopFn,
      handleValueChange: () => {},
      handleUnitChange: nopFn,
      handleNumberScaleChange: nopFn,
      status: 'created',

      // Input values
      questionValue: undefined,
      valueDataData: [],

      // Multi-row table
      table: tableData,
      updateTable: () => {},
      placeholder: 'Test Placeholder',
      saving: false,
      prefix: '',
      suffix: '',
      index: 1,
      unit: 'mt',
      numberScale: '',
      displayCheckbox: {} as DisplayCheckBox,
      scrollToRef: nopFn,
      calculationColumns: [],
      visibilityRuleColumns: [],
      tableConfiguration: {
        columns: utr.valueValidation?.table?.columns ?? [],
      },
    };
  };

  const requiredColumnInputs = [
    { code: 'requiredCol1', value: 'text' },
    { code: 'requiredCol2', value: '10' },
  ];

  it('should enable/disable the "Save to table" button based on required columns fill-in when adding a new row', async () => {
    const tableData: TableDataInfo = { editRowId: -1, rows: [] };
    const store = reduxFixtureStore();

    const view = renderWithProviders(
      <TableInputProvider>
        <AIAssistantContextProvider
          initiativeId='test-initiative'
          utrvId='test-utrv'
          utr={tableUtrWithRequiredColumns}
        >
          <MultiRowTableInput {...getProps({ utr: tableUtrWithRequiredColumns, tableData })} />
        </AIAssistantContextProvider>
      </TableInputProvider>,
      {
        store
      }
    );

    const container = view.baseElement;

    const addButton = screen.getByRole('button', { name: 'Save to table' });
    expect(addButton).toBeDisabled();

    setInputFields(container, requiredColumnInputs);
    expect(addButton).not.toBeDisabled();
    await userEvent.click(addButton);

    expect(screen.getByRole('button', { name: /Add row/ })).toBeInTheDocument();
  });

  it('should enable/disable the "Update row" button based on required columns fill-in when editing an existing row', async () => {
    const tableData: TableDataInfo = {
      editRowId: 0, // edit the first row
      rows: [
        {
          id: 0,
          rowStatus: RowStatus.original,
          isRemoved: false,
          isEdited: false,
          hasChanged: false,
          data: [], // pretend we saved empty data before
        },
      ],
    };

    const store = reduxFixtureStore();

    const view = renderWithProviders(
      <TableInputProvider>
        <AIAssistantContextProvider
          initiativeId='test-initiative'
          utrvId='test-utrv'
          utr={tableUtrWithRequiredColumns}
        >
          <MultiRowTableInput {...getProps({ utr: tableUtrWithRequiredColumns, tableData })} />
        </AIAssistantContextProvider>
      </TableInputProvider>,
      {
        store
      }
    );

    const container = view.baseElement;

    const editButton = screen.getByRole('button', { name: 'Update row' });
    expect(editButton).toBeDisabled();

    setInputFields(container, requiredColumnInputs);
    expect(editButton).not.toBeDisabled();
  });
});