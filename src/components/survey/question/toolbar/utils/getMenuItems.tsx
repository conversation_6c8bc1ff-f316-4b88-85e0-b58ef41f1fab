/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import React, { Dispatch } from 'react';
import { loadUniversalTrackerModal } from '../../../../../actions/universalTrackerModal';
import { CurrentUserData } from '../../../../../reducers/current-user';
import { SurveyPermissions } from '../../../../../services/permissions/SurveyPermissions';
import { AssuranceButton } from '../../../../assurance/assurance-button';
import DelegationModal from '../../../../delegation-modal';
import CompositeValueHistory from '../../../../value-history/CompositeValueHistory';
import { QuestionMenuProps } from '../../QuestionMenu';
import { ToolbarButtonProps } from '../ToolbarButton';
import { UniversalTrackerType } from '../../../../../model/UniversalTracker';
import { QUESTION } from '@constants/terminology';
import { UtrvProof } from '../../../../../apps/company-tracker/components/utr-modal/UtrvProof';
import config from '../../../../../config';
import { FeatureStability } from '@g17eco/molecules/feature-stability';

interface ActionMenuItemProps extends QuestionMenuProps {
  dispatch: Dispatch<any>;
  canAccessAssurance: boolean;
  user: CurrentUserData;
}

interface MenuItemButtonProps extends ToolbarButtonProps {
  id: ModalView;
}

export enum ModalView {
  Delegation = 'delegation',
  History = 'history',
  Provenance = 'provenance',
  Assurance = 'assurance',
  Ledger = 'ledger',
  TagManagement = 'mapping',
}

const MenuItemContent = (props: { icon: string; label: string; suffix?: React.ReactElement }) => {
  const { icon, label, suffix } = props;
  return (
    <div className='d-flex gap-2 align-items-center'>
      <i className={`fal ${icon} text-ThemeIconSecondary fs-5 text-center`} style={{ width: '1.25rem' }} />
      {label}
      {suffix}
    </div>
  );
};

export function getMenuItems(props: ActionMenuItemProps): MenuItemButtonProps[] {
  const { utrv, survey, isCompleted, canAccessAssurance, dispatch, user, alternativeCode, handleReload } = props;

  const canAccessAllData = SurveyPermissions.canAccessAllData(survey, user);
  const isUserStaff = user.isStaff && config.ledger.enabled;

  const isReadOnly = Boolean(survey.completedDate);

  const items: MenuItemButtonProps[] = [
    {
      id: ModalView.Delegation,
      contents: <MenuItemContent icon='fa-user-plus' label='Delegation' />,
      tooltip: `View/Edit this ${QUESTION.SINGULAR}'s contributors and verifiers`,
      disabled: !canAccessAllData,
      modal: {
        component: DelegationModal,
        utrvs: [utrv],
        // Issue: [GU-6093] lost question data when updating delegation
        // Root cause: unnecessary survey reload when we already reload survey users
        handleSubmit: handleReload,
        survey: props.survey,
        initiativeId: props.survey.initiativeId,
        isReadonly: isCompleted,
      },
    },
    {
      id: ModalView.History,
      contents: <MenuItemContent icon='fa-clock-rotate-left' label='Data History' />,
      tooltip: `View this ${QUESTION.SINGULAR}'s data history`,
      onClick: () =>
        dispatch(
          loadUniversalTrackerModal({
            universalTrackerId: utrv.universalTrackerId,
            universalTrackerType: UniversalTrackerType.Utr,
            actionValueId: utrv._id,
            params: { surveyId: survey._id },
            alternativeCode,
          }),
        ),
    },
    {
      id: ModalView.Provenance,
      contents: <MenuItemContent icon='fa-clipboard-list' label='Data Provenance' />,
      tooltip: `View this ${QUESTION.SINGULAR}'s data provenance`,
      modal: {
        component: CompositeValueHistory,
        isReadOnly: isReadOnly,
        isModal: true,
        ids: [utrv._id],
      },
    },
  ];

  if (isUserStaff) {
    items.push({
      id: ModalView.Ledger,
      contents: <MenuItemContent icon='fa-chain' label='Ledger Consistency Check' />,
      tooltip: `View this ${QUESTION.SINGULAR}'s data consistency check`,
      modal: {
        component: UtrvProof,
        isModal: true,
        utrvId: utrv._id,
      },
    });
  }

  const { text, onClick } = AssuranceButton(props);
  if (text && canAccessAssurance) {
    items.push({
      id: ModalView.Assurance,
      contents: <MenuItemContent icon='fa-award' label='Assurance' />,
      disabled: !canAccessAllData,
      tooltip: text,
      onClick,
    });
  }

  if (user.isStaff) {
    items.push({
      id: ModalView.TagManagement,
      contents: (
        <MenuItemContent icon='fa-tags' label='Tag Management' suffix={<FeatureStability stability={'internal'} />} />
      ),
      onClick: props.toggleMapping,
    });
  }

  return items;
}
