/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import React, { useMemo } from 'react';
import { UserMin } from '../../../../constants/users';
import { naturalSort } from '../../../../utils';
import { SurveySettings } from '../../../../types/survey';
import { useAppSelector } from '../../../../reducers';
import { QuestionStatuses } from '../../../../constants/status';
import { FeaturePermissions } from '../../../../services/permissions/FeaturePermissions';
import type { Scope } from '../../../../model/surveyData';
import { useGetCustomTagsQuery } from '../../../../api/metric-groups';
import { noTagOption } from '../../utils/filters';
import { QUESTION } from '@constants/terminology';
import { ExtendedFilters } from '@components/survey/utils/getDisableUtrs';
import { getSDGOptions } from '@features/sdg';
import { Option, SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import { ModuleDropdown } from '@components/module-dropdown';
import { getRootConfig } from '@selectors/globalData';

export type FilterSettings = Omit<SurveySettings, 'groupBy' | 'viewLayout'>;

const getQuestionStatusOptions = ({ canAccessVerification, canAccessAssurance }: { canAccessVerification: boolean, canAccessAssurance: boolean }
  ): Option<QuestionStatuses>[] => {

  const getOption = (name: string, icon: string | JSX.Element, colour: string, faded: boolean = false) => {
    const iconElement = typeof icon === 'string' ? <i className={`fal ${icon} ${faded ? 'faded' : ''} ${colour} mr-1`} /> : icon;
    return (
      <>
        {iconElement}
        {name}
      </>
    );
  }
  const getNANROption = (name: string, text: string) => {
    return (
      <div>
        <span className={'icon mr-1'}>{text}</span>
        {name}
      </div>
    );
  }
  const items: Option<QuestionStatuses>[] = [
    {
      value: QuestionStatuses.Public,
      searchString: `Public ${QUESTION.PLURAL}`,
      label: getOption(`Public ${QUESTION.PLURAL}`, 'fa-eye', 'text-chateauGrey')
    },
    {
      value: QuestionStatuses.Private,
      searchString: `Private ${QUESTION.PLURAL}`,
      label: getOption(`Private ${QUESTION.PLURAL}`, 'fa-eye-low-vision', 'text-chateauGrey')
    },
    {
      value: QuestionStatuses.NR,
      searchString: `Not reporting ${QUESTION.PLURAL}`,
      label: getNANROption(`Not reporting ${QUESTION.PLURAL}`, 'N/R')
    },
    {
      value: QuestionStatuses.NA,
      searchString: `Not applicable ${QUESTION.PLURAL}`,
      label: getNANROption(`Not applicable ${QUESTION.PLURAL}`, 'N/A')
    },
  ];

  if (canAccessVerification) {
    items.unshift({
      value: QuestionStatuses.Rejected,
      searchString: `Rejected ${QUESTION.PLURAL}`,
      label: getOption(`Rejected ${QUESTION.PLURAL}`, 'fa-user-times', 'text-danger')
    });
  }

  items.unshift({
    value: QuestionStatuses.Verified,
    searchString: `Verified ${QUESTION.PLURAL}`,
    label: getOption(`Verified ${QUESTION.PLURAL}`, 'fa-user-check', 'text-success')
  });

  if (canAccessVerification) {
    items.unshift({
      value: QuestionStatuses.Updated,
      searchString: `Updated ${QUESTION.PLURAL}`,
      label: getOption(`Updated ${QUESTION.PLURAL}`, 'fa-user-edit', 'text-primary')
    });
  }

  items.unshift({
    value: QuestionStatuses.Created,
    searchString: `Unanswered ${QUESTION.PLURAL}`,
    label: getOption(`Unanswered ${QUESTION.PLURAL}`, 'fa-user-clock', 'text-muted'),
  });

  if (canAccessAssurance) {
    items.push({
      value: '' as QuestionStatuses,
      label: <span className='text-uppercase text-ThemeTextDark'>Assurance</span>,
      searchString: 'assurance',
      isDisabled: true,
    });
    const assuredText = 'Assured';
    items.push({
      value: QuestionStatuses.Assured,
      searchString: assuredText,
      label: getOption(assuredText, 'fa-award', 'text-ThemeSuccessMedium'),
    });

    const partiallyAssuredText = 'Partially assured';
    items.push({
      value: QuestionStatuses.PartiallyAssured,
      searchString: partiallyAssuredText,
      label: getOption(
        partiallyAssuredText,
        'fa-award partially-assured-icon',
        ''
      ),
    });

    const notAssuredText = 'Not assured';
    items.push({
      value: QuestionStatuses.NotAssured,
      searchString: notAssuredText,
      label: getOption(
        notAssuredText,
        'fa-award',
        'text-ThemeWarningMedium'
      ),
    });

    items.push({
      value: QuestionStatuses.Disputed,
      searchString: 'Disputed assurance',
      label: getOption('Disputed assurance', 'fa-award', 'text-ThemeDangerMedium'),
    });
    items.push({
      value: QuestionStatuses.Restated,
      searchString: 'Restated',
      label: getOption('Restated', 'fa-rotate', 'text-ThemeSuccessMedium'),
    });
    items.push({
      value: QuestionStatuses.SentToAssurer,
      searchString: 'Selected for assurance',
      label: getOption('Selected for assurance', 'fal fa-award', 'text-ThemeAccentDark'),
    });

    const notSelectedForAssuranceText = 'Not selected for assurance';
    items.push({
      value: QuestionStatuses.NotSelectedForAssurance,
      searchString: notSelectedForAssuranceText,
      label: getOption(notSelectedForAssuranceText, 'fa-award', 'text-ThemeTextLight'),
    });
  }

  return items;
}

const getDelegationStatusOptions = ({ users, canAccessVerification }:
  { users?: UserMin[], canAccessVerification: boolean }): Option<string>[] => {
  const getOption = (typeName: string, icon: string, colour: string, faded: boolean = false) => {
    return (
      <div className='w-100'>
        {icon ? <i style={{ width: '18px' }} className={`fa ${icon} ${faded ? 'faded' : ''} ${colour} mr-1`} /> : null}
        {typeName}
      </div>
    );
  };
  const options: Option<string>[] = [
    {
      value: 'user-a',
      label: getOption('Assigned to me', 'fa-user', 'text-chateauGrey', false),
    },
  ];

  if (canAccessVerification) {
    options.push(
      {
        label: 'Assigned',
        value: '',
        isDisabled: true,
      },
      {
        value: 'con-a',
        searchString: 'Contributor Assigned',
        label: getOption('Contributor', 'fa-user-edit', 'text-primary', false),
      },
      {
        value: 'ver-a',
        searchString: 'Verifier Assigned',
        label: getOption('Verifier', 'fa-user-check', 'text-success', false),
      },
      {
        label: 'Unassigned',
        value: '',
        isDisabled: true,
      },
      {
        value: 'con-u',
        searchString: 'Contributor Unassigned',
        label: getOption('Contributor', 'fa-user-edit', 'text-chateauGrey', true),
      },
      {
        value: 'ver-u',
        searchString: 'Verifier Unassigned',
        label: getOption('Verifier', 'fa-user-check', 'text-chateauGrey', true),
      }
    );
  }

  if (users) {
    options.push(
      {
        label: 'Users',
        value: '',
        isDisabled: true,
      },
      ...users
        .filter((u) => u._id)
        .sort((a, b) => naturalSort(`${a.firstName} ${a.surname}`, `${b.firstName} ${b.surname}`))
        .map((u) => ({
          value: u._id,
          searchString: `${u.firstName} ${u.surname}`,
          label: getOption(`${u.firstName} ${u.surname}`, '', ''),
        }))
    );
  }

  return options;
};

export const onChangeHandlerSelectWrapper = (
  key: keyof SurveySettings,
  handleChangeSettings: (key: keyof SurveySettings, settings: string[]) => void
) => {
  return (selectedOption: Option<string> | null | Option<string>['value'][]) => {
    if (Array.isArray(selectedOption)) {
      handleChangeSettings(key, selectedOption);
    } else if (!selectedOption || selectedOption.value === null) {
      handleChangeSettings(key, []);
    } else {
      handleChangeSettings(key, [selectedOption.value])
    }
  }
};

interface FilterQuestionsProps {
  initiativeId: string;
  settings: ExtendedFilters;
  handleChangeSettings: (key: keyof SurveySettings, setting: string[]) => void;
  isAggregate?: boolean;
  surveyScope?: Scope;
}

export const FilterQuestions = (props: FilterQuestionsProps) => {
  const { initiativeId = '', settings, handleChangeSettings, isAggregate = false, surveyScope } = props;
  const rootConfig = useAppSelector(getRootConfig);
  const canAccessAssurance = FeaturePermissions.canAccessAssurance(rootConfig);
  const canAccessVerification = FeaturePermissions.canAccessVerification(rootConfig);

  const {
    filterByStatus,
    filterByDelegationStatus,
    filterByGoal,
    filterByModules,
    filterByTag,
  } = settings;

  const users: UserMin[] = useAppSelector((state) => state.surveyDelegationUsers.data.users ?? []);
  const { data: tags = [] } = useGetCustomTagsQuery(initiativeId, { skip: !initiativeId || !Array.isArray(filterByTag) });

  const questionStatusOptions = getQuestionStatusOptions({ canAccessAssurance, canAccessVerification });
  const delegationStatusOptions = getDelegationStatusOptions({ users, canAccessVerification });
  const sdgOptions = getSDGOptions();

  const tagOptions = useMemo(
    () => {
      const options = [noTagOption];
      tags.forEach((tag) => {
        options.push({ value: tag._id, searchString: tag.groupName, label: tag.groupName })
      })
      return options;
    },
    [tags]
  );

  const onChange = (key: keyof ExtendedFilters) => onChangeHandlerSelectWrapper(key, handleChangeSettings);

  return (
    <div className='d-flex flex-wrap align-items-center gap-2 mx-0 filter-questions'>
      {!isAggregate && Array.isArray(filterByStatus) ? (
        <div className='filter-questions__item'>
          <SelectFactory
            selectType={SelectTypes.MultipleSelect}
            placeholder='Status'
            options={questionStatusOptions}
            onChange={onChange('filterByStatus')}
            values={filterByStatus}
          />
        </div>
      ) : null}

      {!isAggregate && Array.isArray(filterByDelegationStatus) ? (
        <div className='filter-questions__item'>
          <SelectFactory<string>
            selectType={SelectTypes.MultipleSelect}
            placeholder='Delegation'
            options={delegationStatusOptions}
            onChange={onChange('filterByDelegationStatus')}
            values={filterByDelegationStatus}
          />
        </div>
      ) : null}

      {Array.isArray(filterByModules) ? (
        <div className='filter-questions__item'>
          <ModuleDropdown
            initiativeId={initiativeId}
            onChange={onChange('filterByModules')}
            modules={filterByModules}
            scope={surveyScope}
          />
        </div>
      ) : null}

      {Array.isArray(filterByGoal) ? (
        <div className='filter-questions__item'>
          <SelectFactory
            selectType={SelectTypes.MultipleSelect}
            placeholder='SDGs'
            options={sdgOptions}
            onChange={onChange('filterByGoal')}
            values={filterByGoal}
          />
        </div>
      ) : null}

      {Array.isArray(filterByTag) ? (
        <div className='filter-questions__item'>
          <SelectFactory
            selectType={SelectTypes.MultipleSelect}
            placeholder='Tags'
            options={tagOptions}
            onChange={onChange('filterByTag')}
            values={filterByTag}
          />
        </div>
      ) : null}
    </div>
  );
}
