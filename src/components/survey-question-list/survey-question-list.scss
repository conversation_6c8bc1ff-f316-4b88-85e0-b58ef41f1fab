/*!
 * Copyright (c) 2019. World Wide Generation Ltd
 */

@import "src/css/variables";

.questionListGroup {
  border: none !important;
  .questionListGroup .trackingListRow {
    opacity: 0.3;
  }
  .trackingListRow {
    line-height: 13px;
    background-color: var(--theme-BgCanvas);
    border: 0px;

    &.clickable {
      opacity: 1;
      &:hover {
        filter: brightness(90%);
        text-decoration: none !important;
      }
      .fa {
        font-size: 1rem;
      }
    }
    .trackingListColumn {
      &.statsCol {
        display: flex;
        justify-content: flex-end;
        padding: 0 0.5rem 0 1rem;
        border-radius: $borderRadius;
        line-height: 22px;
        min-width: 52px;
        background-color: transparent;
        border: none;
      }
    }
    .trackingListDivider {
      margin: 0;
    }
  }
}

.sub__group__title {
  .trackingListDivider {
    margin: 0;
  }

  .clickable {
    i {
      width: 6px;
      font-size: 13px;
    }
    &:hover {
      cursor: pointer;
      i {
        filter: brightness(85%);
      }
    }
  }
}

.question-list__select-box {
  text-align: center;
  padding: 0 !important;
  min-width: 35px;
  .btn i.fa-square {
    &:hover {
      color: var(--theme-AccentLight) !important;
    }
  }
}

.sub__group__title {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  font-size: 11px;
  line-height: 11px;
  letter-spacing: 5%;
  font-weight: 500;
  text-transform: uppercase;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--theme-HeadingLight);
}

.questionList {
  .trackingListRow {
    .trackingListColumn {
      &.codeCol {
        width: 90px;
        min-width: 90px;
      }
      &.flagsCol {
        min-width: 76px;
        width: 76px;
      }
      &.commentFlagCol {
        min-width: 21px;
        width: 21px;
      }
      &.valueCol {
        text-align: left;
        min-width: 200px;
        width: 200px;
        .renderNumericValue {
          width: 200px;
        }
        span.prefix-and-value {
          display: inline-block;
          max-width: 50%;
        }

        span.suffix {
          display: inline-block;
          max-width: 50%;
          text-align: left;
        }
        span.prefix,
        span.suffix {
          color: var(--theme-TextPlaceholder);
        }
        span.badge {
          display: inline-block;
          margin-right: 40%;
        }
      }
      &.statusCol {
        text-align: center;
        padding: 0 0 0 0.5rem !important;
        min-width: 30px;
      }
      &.stakeholderCol {
        min-width: 250px;
        width: 250px;
      }

      .muted,
      i.muted {
        color: var(--theme-TextPlaceholder) !important;
      }

      i {
        &.fixed-width {
          min-width: 20px;
        }
        &.created {
          color: var(--theme-TextPlaceholder);
        }
        &.verified {
          color: var(--theme-SuccessMedium);
        }
        &.assured {
          font-size: 1rem;
          color: var(--theme-SuccessMedium);
        }
        &.assurance-disputed {
          font-size: 1rem;
          color: var(--theme-DangerMedium);
        }
        &.updated {
          color: var(--theme-AccentMedium);
        }
        &.rejected {
          color: var(--theme-DangerMedium);
        }
        &.fa-bookmark {
          color: var(--theme-NeutralsDark);
          font-size: 12px;
        }
        &.partially-assured-icon {
          font-size: 1rem;
        }
      }

      &.question-list__options-menu {
        min-width: 52px;
      }
    }
  }
  @keyframes blink {
    0% {
      background-color: var(--theme-AccentExtralight);
    }
    50% {
      background-color: transparent;
    }
    100% {
      background-color: var(--theme-AccentExtralight);
    }
  }
  @-webkit-keyframes blink {
    0% {
      background-color: var(--theme-AccentExtralight);
    }
    50% {
      background-color: transparent;
    }
    100% {
      background-color: var(--theme-AccentExtralight);
    }
  }

  .highlight-blink {
    -moz-animation:blink normal 0.5s infinite ease-in-out;
    /* Firefox */
    -webkit-animation:blink normal 0.5s infinite ease-in-out;
    /* Webkit */
    animation:blink normal 0.5s infinite ease-in-out;
    /* Opera */
    animation-iteration-count: 3;

    background-color: var(--theme-AccentExtralight);
  }
}

.scopeGroupTitle {
  margin: 40px 0 8px;
  color: var(--theme-AccentExtradark);
  .scopeGroupName {
    padding: 0 8px 0 16px;
    border-right: 1px solid var(--theme-AccentExtradark);
    margin-right: 8px;
    line-height: normal;
    text-transform: uppercase;
  }
}

.survey-question-list__sticky-toolbar {
  pointer-events: none;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  button {
    pointer-events: all;
  }
}
