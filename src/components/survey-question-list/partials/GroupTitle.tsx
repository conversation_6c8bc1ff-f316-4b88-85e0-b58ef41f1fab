/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import React from 'react';
import { ScopeQuestionGroupOptionalValue } from '../../../types/survey';
import { safeColours } from '@g17eco/molecules/form';
import { BulkActionToolbarSelectedUtrv } from './BulkActionToolbar';
import { GroupSelectbox } from './GroupSelectBox';
import { QUESTION } from '@constants/terminology';
import { countVerifiedQuestion } from '../utils';
import { Column, Row, TrackingList } from '@g17eco/molecules/tracking-list';
import { InheritedIcon } from '@features/survey/scope-group/InheritedIcon';
import { getGroup } from '@g17eco/core';

interface GroupTitleProps {
  isOpen: boolean;
  toggle?: () => void;
  questionGroup: ScopeQuestionGroupOptionalValue;
  rowRef?: React.ForwardedRef<HTMLDivElement>;
  enableBulkActions: boolean;
  selectedQuestionIds: string[];
  toggleSelectQuestions: (utrvs: BulkActionToolbarSelectedUtrv[]) => void;
}

const FIXED_SIZE = '24px';

const GroupLogo = ({ questionGroup }: { questionGroup: ScopeQuestionGroupOptionalValue }) => {
  if (!questionGroup.groupData) {
    return null;
  }
  const groupLogo =
    getGroup('standards-and-frameworks', questionGroup.groupCode || '')?.src || questionGroup.groupData?.icon;
  if (groupLogo) {
    return (
      <img
        src={groupLogo}
        alt={questionGroup.groupName}
        style={{ width: FIXED_SIZE, maxWidth: FIXED_SIZE, maxHeight: FIXED_SIZE }}
        className='mr-2'
      />
    );
  }
  return (
    <i
      className='fa fa-circle mr-2'
      style={{ color: questionGroup.groupData?.colour ?? 'transparent', fontSize: FIXED_SIZE }}
    />
  );
};

export const GroupTitle = (props: GroupTitleProps) => {
  const { toggle, isOpen, questionGroup, rowRef, enableBulkActions, selectedQuestionIds, toggleSelectQuestions } = props;

  const questionList = questionGroup.list ?? [];
  const hasQuestions = questionList && questionList.length > 0;
  const icon = !hasQuestions ? '' : isOpen ? 'fa-caret-down' : 'fa-caret-right';
  const colour = questionGroup.groupData && questionGroup.groupData.colour;

  const isSafeColour = !colour || safeColours.includes(colour);
  const onClick = hasQuestions ? () => toggle?.() : undefined;

  const style = colour ? { backgroundColor: colour, borderColor: colour } : undefined;
  const isScopeView = Boolean(questionGroup.count);

  if (isScopeView) {
    return (
      <Row className='scopeGroupTitle'>
        <Column className='h4 scopeGroupName'><GroupLogo questionGroup={questionGroup} />{questionGroup.groupName}</Column>
        <Column >{`${questionGroup.count} ${QUESTION.SINGULAR}${questionGroup.count === 1 ? '' : 's'}`}</Column>
      </Row>
    );
  }

  return (
    <TrackingList className={'questionListGroup mb-1'}>
      <Row ref={rowRef} onClick={toggle ? onClick : null} style={style} className={!isSafeColour ? 'text-ThemeTextWhite' : ''}>
        {toggle
          ? <Column><i className={`ml-2 text-ThemeIconSecondary fa-light ${icon}`} /></Column>
          : <Column className='px-1' />
        }
        <Column stretch truncate className='d-flex align-items-center'>
          {questionGroup.groupData?.isInherited ? <InheritedIcon /> : null}
          <GroupLogo questionGroup={questionGroup} />
          {questionGroup.groupName}
        </Column>
        <Column className='statsCol dont_translate'>{countVerifiedQuestion(questionList)} / {questionGroup.count ?? questionList.length}</Column>
        {enableBulkActions ? (
          <GroupSelectbox
            questionGroup={questionGroup}
            questionList={questionList}
            selectedQuestionIds={selectedQuestionIds}
            toggleSelectQuestions={(utrvs) => toggleSelectQuestions(utrvs)}
          />
        ) : null}
      </Row>
    </TrackingList>
  );
}
