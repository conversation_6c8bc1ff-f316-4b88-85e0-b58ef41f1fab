/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { useHistory } from 'react-router-dom';
import { ROUTES } from '@constants/routes';
import { SurveyModelMinimalUtrv } from '../../../model/surveyData';
import { generateUrl } from '@routes/util';
import {
  getStatusIcon,
  getStatusText,
  isUtrvAssuranceComplete,
  isUtrvAssuranceRejected,
  isUtrvPartialAssurance,
} from '@utils/universalTrackerValue';
import { ColumnCommonProps } from '@g17eco/types/survey-question-list';
import { Column } from '@g17eco/molecules/tracking-list';

type StatusIcon = {
  icon: string;
  customIcon?: JSX.Element;
  onClick?: () => void;
  disabled: boolean;
  className: string;
  tooltip?: string | JSX.Element;
};

const getClassName = (utrv: Pick<SurveyModelMinimalUtrv, 'status' | 'assuranceStatus' | 'lastUpdated'>) => {
  switch (true) {
    case isUtrvAssuranceComplete(utrv.assuranceStatus):
      return 'assured';
    case isUtrvAssuranceRejected(utrv.assuranceStatus):
      return 'assurance-disputed';
    case isUtrvPartialAssurance(utrv.assuranceStatus):
      return 'partially-assured-icon';
    default:
      return `${utrv.status}`;
  }
};

const getStatusIconProps = (
  utrv: Pick<SurveyModelMinimalUtrv, 'status' | 'assuranceStatus' | 'lastUpdated'>,
  goToAssurancePage: () => void,
): StatusIcon => {
  if (!utrv) {
    return {
      icon: '',
      onClick: undefined,
      disabled: true,
      className: '',
      tooltip: undefined,
    };
  }

  const isClickable =
    isUtrvAssuranceComplete(utrv.assuranceStatus) ||
    isUtrvAssuranceRejected(utrv.assuranceStatus) ||
    isUtrvPartialAssurance(utrv.assuranceStatus);

  return {
    icon: getStatusIcon(utrv),
    onClick: isClickable ? goToAssurancePage : undefined,
    disabled: false,
    className: getClassName(utrv),
    tooltip: getStatusText(utrv),
  };
};

export const ColumnStatusIcon = (props: ColumnCommonProps) => {
  const { question, surveyId, initiativeId } = props;

  const history = useHistory();
  if (!question.utrv) {
    return null;
  }

  const goToAssurancePage = () =>
    history.push(generateUrl(ROUTES.COMPANY_TRACKER_SURVEY, { initiativeId, surveyId, page: 'assurance' }));

  const statusIcon = getStatusIconProps(question.utrv, goToAssurancePage);

  return (
    <Column
      className='statusCol'
      disabled={statusIcon.disabled}
      onClick={statusIcon.onClick}
      tooltip={statusIcon.tooltip}
    >
      {statusIcon.customIcon ? (
        statusIcon.customIcon
      ) : (
        <i className={`fal ${statusIcon.icon} fixed-width ${statusIcon.className}`} />
      )}
    </Column>
  );
};
