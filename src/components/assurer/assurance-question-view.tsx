/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import React, { useEffect, useMemo, useState } from 'react';
import Dashboard, { DashboardSection } from '@g17eco/molecules/dashboard';
import { Button } from 'reactstrap';
import { generateUrl } from '../../routes/util';
import { ROUTES } from '../../constants/routes';
import {
  AssurancePortfolioStatus,
  AssuranceStatus,
  OrganizationAssurancePortfolio,
} from '../../types/assurance';
import { useHistory, useLocation } from 'react-router-dom';
import { standards } from '@g17eco/core';
import { NotApplicableBadge } from '@g17eco/molecules/question-status/NotApplicableBadge';
import { nl2br } from '../../utils';
import { QuestionInput } from '../question/QuestionInput';
import { getQuestionId } from '../../selectors/survey';
import { NavigationButtons } from '../question/NavigationButtons';
import { SupportingEvidenceTooltip } from '../utr-confirmation-modal/SupportingEvidenceTooltip';
import { loadUtrvHistoryAssurer } from '../../actions/universalTracker';
import { UtrvHistory, ValueHistory } from '../../types/universalTrackerValue';
import { getExistingFiles } from '../survey/question/QuestionReducer';
import { ExistingEvidenceFile } from '../survey/question/questionInterfaces';
import { EvidenceContainer } from '../files/EvidenceContainer';
import { loadUniversalTrackerModal } from '../../actions/universalTrackerModal';
import CompositeValueHistory from '../value-history/CompositeValueHistory';
import { ScopeQuestionGroup } from '../../types/survey';
import { useAppDispatch } from '../../reducers';
import UtrvComments from '../utrv-comments';
import G17Client from '../../services/G17Client';
import { UtrvStatus } from '../../constants/status';
import { downloadQuestionBundle } from '../../actions/assurance';
import { getAnalytics } from '../../services/analytics/AnalyticsService';
import { AnalyticsEvents } from '../../services/analytics/AnalyticsEvents';
import { addSiteAlert, SiteAlertColors } from '../../slice/siteAlertsSlice';
import { CollapseButton, CollapseContent, CollapsePanel } from '@g17eco/molecules/collapse-panel';
import { Comment } from '../utr-confirmation-modal/Comment';
import { QuestionStatus } from './QuestionStatus';
import './assurance-question-view.scss';
import '../utr-confirmation-modal/comments.scss';
import { DisputeButton } from './toolbar-buttons/DisputeButton';
import { useQuestionIds } from '../../hooks/useQuestionIds';
import { ShowAs } from '../../reducers/universal-tracker-modal';
import { UniversalTrackerType } from '../../model/UniversalTracker';
import { QUESTION } from '@constants/terminology';
import { RichTextEditorContainer } from '@features/rich-text-editor';
import { usePartialAssurance } from '@hooks/usePartialAssurance';
import { useUpdatePartialAssuranceMutation } from '@api/assurance';
import { Loader } from '@g17eco/atoms/loader';
import { UtrValueType } from '@g17eco/types/universalTracker';
import { useSiteAlert } from '@hooks/useSiteAlert';
import { useGetUtrvCommentsQuery } from '../../api/utrv-comments';
import { skipToken } from '@reduxjs/toolkit/query';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';
import { StatusBadge } from '@g17eco/molecules/question-status/StatusBadge';
import { UnitConfig } from '@models/surveyData';

interface Props {
  assurancePortfolio: OrganizationAssurancePortfolio;
  surveyGroups: ScopeQuestionGroup[];
  questionId: string,
  index?: string;
  handleReload: () => void;
  canAssure: boolean;
  unitConfig?: UnitConfig;
}

const defaultHistory: {
  stakeholderHistory?: { note?: string };
  verifierHistory?: { note?: string },
  loaded: boolean;
} = {
  stakeholderHistory: {},
  verifierHistory: {},
  loaded: false
};

export const AssuranceQuestionView = (props: Props) => {

  const { assurancePortfolio, surveyGroups, questionId, index, handleReload, canAssure, unitConfig } = props;

  const [evidenceFiles, setEvidenceFiles] = useState<{ loaded: boolean, data?: ExistingEvidenceFile[] }>({ loaded: false });
  const [openProvenanceModal, setOpenProvenanceModal] = useState(false);
  const [openComments, setOpenComments] = useState(false);
  const [utrvHistory, setUtrvHistory] = useState(defaultHistory);
  const [commentId, setCommentId] = useState('');

  const { addSiteError } = useSiteAlert();

  const dispatch = useAppDispatch();
  const history = useHistory();
  const location = useLocation();
  const assurancePortfolioId = assurancePortfolio._id;
  const surveyId = assurancePortfolio.surveyId;

  useEffect(() => {
    if (location.search) {
      const searchParams = new URLSearchParams(location.search);
      const id = searchParams.get('commentId');
      if (id) {
        setCommentId(id);
        setOpenComments(!!id);
        searchParams.delete('commentId');
        history.replace({
          pathname: location.pathname,
          search: searchParams.toString(),
        });
      }
    }
  }, [history, location.pathname, location.search]);

  const { questionListIds, utr, utrv, altCode } = useQuestionIds(surveyGroups, index, questionId);
  const { data: utrvComments, isFetching } = useGetUtrvCommentsQuery(utrv ? utrv._id : skipToken);
  const hasUtrvComments = Boolean(utrvComments?.items.length);

  const assuranceUtrv = useMemo(
    () => {
      const foundAssuranceUtrv = assurancePortfolio.universalTrackerValueAssurances.find(a => a.utrvId === questionId);
      if (foundAssuranceUtrv) {
        // populate utrv to assuranceUtrv for displaying status icon
        return { ...foundAssuranceUtrv, utrv };
      }
      return foundAssuranceUtrv;
    },
    [assurancePortfolio, questionId, utrv]
  );

  const [updatePartialAssurance, { isLoading: partialAssuranceLoading }] = useUpdatePartialAssuranceMutation();
  const { partialFields, addons, isSelectingAll, isSelectedSameFields } = usePartialAssurance(utr, utrv, assuranceUtrv);

  React.useEffect(() => {
    loadUtrvHistoryAssurer(assurancePortfolioId, questionId)
      .then((respData: UtrvHistory) => {
        setEvidenceFiles({
          data: getExistingFiles(respData),
          loaded: true
        });

        const { stakeholderHistory, verifierHistory } = respData.latestHistory;
        setUtrvHistory({ stakeholderHistory, verifierHistory, loaded: true })
      })
      .catch(() => setEvidenceFiles({ loaded: true }));
  }, [assurancePortfolioId, questionId]);

  const goToQuestion = ({ id, index }: { id: any, index?: number }) => {
    const url = generateUrl(ROUTES.ASSURANCE_PORTFOLIO, {
      assurancePortfolioId,
      questionId: id,
      index: String(index ?? '')
    })
    history.push({ pathname: url, search: location.search });
  };

  const goNext = () => goToQuestion(getQuestionId(questionListIds, questionId, index ?? '', true));
  const goPrevious = () => goToQuestion(getQuestionId(questionListIds, questionId, index ?? '', false));

  const handleAssureQuestion = async () => {
    if (!assuranceUtrv) {
      return;
    }
    await G17Client.assureQuestions(assurancePortfolioId, { questions: [assuranceUtrv?._id] });
    handleReload();
  }

  const handlePartiallyAssureQuestion = async () => {
    if (!partialFields.length) {
      return;
    }
    await updatePartialAssurance({ portfolioId: assurancePortfolioId, utrvId: utrv._id, partialFields })
      .unwrap()
      .catch((e) => addSiteError(e.message));
    handleReload();
  }

  const handleDownloadData = () => {
    if (!assuranceUtrv) {
      return;
    }
    downloadQuestionBundle(assurancePortfolioId, assuranceUtrv?._id)
      .then(async ([document]) => {
        if (!document || !document.url) {
          return;
        }
        window.open(document.url, '_blank', '');

        const analytics = getAnalytics();
        return analytics.track(AnalyticsEvents.SurveyDataDownloaded, {
          assurancePortfolioId,
          initiativeId: assurancePortfolio.initiativeId,
          questionId,
          source: 'survey_assurance'
        });
      })
      .catch((e: Error) => dispatch(addSiteAlert({
        content: `Unable to fetch requested data (${e.message})`,
        timeout: 3000,
        color: SiteAlertColors.Danger
      })))
  }

  const onBackClick = () => history.push(generateUrl(ROUTES.ASSURANCE_PORTFOLIO, { assurancePortfolioId }));
  const backSection = <div>
    <Dashboard>
      <div className='d-flex col' style={{ justifyContent: 'space-between' }}>
        <Button color='link' onClick={onBackClick}>
          <i className='fa fa-arrow-circle-left mr-2' />Back to {QUESTION.CAPITALIZED_SINGULAR} List
        </Button>
        <NavigationButtons goNext={goNext} goPrevious={goPrevious} isDisabled={questionListIds.length <= 1} />
      </div>
    </Dashboard>
  </div>;

  if (!utr || !utrv) {
    return (
      <div className='flex-column'>
        {backSection}
        <Dashboard className='assuranceContainer'>
          <DashboardSection padding={0}>
            <div className='p-3'>
              <h5>{QUESTION.CAPITALIZED_SINGULAR} not available</h5>
            </div>
          </DashboardSection>
        </Dashboard>
      </div>
    )
  }

  const standardName = standards[altCode]?.name || '';
  const standardCode = utr.getTypeCode(altCode);

  const isAssured = assuranceUtrv?.status === AssuranceStatus.Completed;
  const isVerified = utrv.status === UtrvStatus.Verified;
  const canBeAssured = canAssure && isVerified && !isAssured;
  const isCompletedAssurance = assurancePortfolio.status === AssurancePortfolioStatus.Completed;

  const instructionsText = `View ${standards[altCode]?.shortName || ''} Instructions`;
  const instructionsLink = utr.getInstructionsLink(altCode);

  const openUtrModal = () => {
    if (!utrv) {
      return;
    }
    return dispatch(
      loadUniversalTrackerModal(
        {
          universalTrackerId: utrv.universalTrackerId,
          universalTrackerType: UniversalTrackerType.Utr,
          actionValueId: utrv._id,
          params: { surveyId },
          openAction: { activeTabId: ShowAs.Table, hideProvenance: true }
        })
    );
  }
  const handleToggle = () => {
    setCommentId('');
    setOpenComments(!openComments);
  };
  const hasComments = [utrvHistory.stakeholderHistory, utrvHistory.verifierHistory].some(h => h?.note);

  const renderActionButtons = () => {
    const buttons = [];

    if ([UtrValueType.NumericValueList, UtrValueType.TextValueList, UtrValueType.Table].includes(utr.getValueType())) {
      buttons.push(
        <Button
          key='partially-assured-button'
          color='primary'
          outline
          disabled={
            !canBeAssured || isCompletedAssurance || !partialFields.length || isSelectingAll || isSelectedSameFields
          }
          className='p-2 px-3 mr-2'
          onClick={() => handlePartiallyAssureQuestion()}
        >
          Assure selected only
        </Button>
      );
    }

    return buttons.concat([
      <DisputeButton
        key='dispute-button'
        isDisabled={isCompletedAssurance || !canAssure}
        utrvId={utrv._id}
        assurancePortfolioId={assurancePortfolioId}
        assuranceUtrv={assuranceUtrv}
        handleReload={handleReload}
        organization={assurancePortfolio.organization}
      />,
      <Button
        key='assured-button'
        color='primary'
        disabled={!canBeAssured || !isSelectingAll || isCompletedAssurance}
        className='p-2 px-3'
        onClick={() => handleAssureQuestion()}
      >
        Assure {QUESTION.SINGULAR}
      </Button>,
    ]);
  };

  return (
    <div className='flex-column'>
      {partialAssuranceLoading ? <Loader /> : null}
      {backSection}
      <Dashboard className='assurance-question-view'>
        <DashboardSection padding={0}>
          {utrv && openComments ? <div className='pt-1 position-relative'>
            <UtrvComments
              toggle={handleToggle}
              utrvId={utrv._id}
              commentId={commentId}
              utrvComments={utrvComments}
              isLoading={isFetching}
              surveyId={surveyId}
            />
          </div> : null}
          <div className='p-3'>

            <div className='mb-3 d-relative text-ThemeTextMedium'>
              <div className='d-flex align-items-center justify-content-between'>
                <div>
                  {`${standardName} - ${standardCode}`}
                </div>
                <div className='question__status d-flex align-items-center'>
                  <div className='fa-xl'>
                    <QuestionStatus assuranceUtrv={assuranceUtrv} />
                  </div>
                  {isVerified ? <StatusBadge utrv={utrv} className='ml-2' /> : null}
                  <NotApplicableBadge utrv={utrv} showExplanation={false} className='ml-2' />
                </div>
              </div>
              <div className='d-flex align-items-center mt-4 justify-content-end text-right'>
                <Button size='sm' color='transparent' className='ml-2' onClick={handleToggle}>
                  {hasUtrvComments ? <i className='far fa-message-lines' /> : <i className='far fa-comment-alt' />}
                </Button>
                {instructionsLink && (
                  <Button color='link' size='sm' onClick={() => window.open(instructionsLink, '_blank')}>
                    <u>{instructionsText}</u>
                  </Button>
                )}
              </div>
            </div>

            <div className='mt-3 pt-2 strong text-xl'>
              {utr.getValueLabel(altCode)}
            </div>

            <div className='mt-3'>
              {nl2br(utr.getInstructions(altCode))}
            </div>

            <div className='mt-4'>
              <QuestionInput
                utrv={utrv}
                utr={utr}
                isReadOnly={true}
                addons={{ after: addons }}
                displayCheckbox={utrv.valueData?.data}
                unitConfig={unitConfig}
              />
              <div className='text-ThemeAccentMedium text-right'>
                <Button color='link' disabled={!utrv} size='sm' onClick={() => openUtrModal()}>
                  <i className='fal fa-clock-rotate-left text-ThemeAccentMedium mr-2' />data history
                </Button>
                <Button color='link' disabled={!utrv} size='sm' onClick={() => setOpenProvenanceModal(!openProvenanceModal)}>
                  <i className='fas fa-clipboard text-ThemeAccentMedium mr-2' />provenance
                </Button>
                {openProvenanceModal &&
                  <CompositeValueHistory isOpen={openProvenanceModal}
                    ids={[utrv._id]}
                    toggle={() => setOpenProvenanceModal(!openProvenanceModal)} />}
              </div>
            </div>

            {(evidenceFiles.data ?? []).length > 0 ?
              <>
                <div className='mt-4 pt-2'>
                  <h5 className='d-flex align-items-center gap-3'>
                    Supporting Evidence
                  <SupportingEvidenceTooltip />
                  </h5>
                </div>
                <div>
                  <EvidenceContainer
                    files={evidenceFiles.data ?? []}
                    allowToggle={false}
                    isReadOnly={true}
                    isLoaded={true}
                    saving={false}
                    metadata={{ initiativeId: assurancePortfolio.initiativeId }}
                  />
                </div>
              </>
              :
              null
            }
            {hasComments ?
              <CollapsePanel className='comment-group comments mt-3 py-3 border-top border-bottom' collapsed>
                <CollapseButton>
                  <h5 className='question-title-container mb-0'>
                    Further explanation / notes
                  </h5>
                </CollapseButton>
                <CollapseContent>
                  <RichTextEditorContainer>
                  <LoadingPlaceholder height={85} isLoading={!utr}>
                    {[utrvHistory.stakeholderHistory, utrvHistory.verifierHistory].map((history, index) => {
                      return history?.note ? (
                        <Comment
                          key={index ? 'verifier' : 'stakeholder'}
                          history={history as Pick<ValueHistory, 'action' | 'note'>}
                          handleComments={() => {}}
                          canEdit={false}
                          canUseRichTextEditor={false}
                        />
                      ) : null;
                    })}
                  </LoadingPlaceholder>
                  </RichTextEditorContainer>
                </CollapseContent>
              </CollapsePanel>
              : null}

            <div className='mt-4 d-flex justify-content-between'>
              <Button outline disabled={!isVerified} className='p-2 px-3 mr-2' onClick={() => handleDownloadData()}>
                <i className='fa-regular fa-file-zipper mr-2' />Download data and evidence
              </Button>
              <div>
                {renderActionButtons()}
              </div>
            </div>
          </div>

        </DashboardSection>
      </Dashboard>
    </div>
  )
};
