/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import React from 'react';
import { SurveyModelMinimalUtrv } from '../../../model/surveyData';
import { UniversalTrackerValueAssurance } from '@g17eco/types/assurance';
import { getStatusIcon, isUtrvAssuranceComplete } from '@utils/universalTrackerValue';
import { ColumnCommonProps } from '@g17eco/types/survey-question-list';
import { QuestionStatus } from '../QuestionStatus';
import { Column } from '@g17eco/molecules/tracking-list';

interface ColumnAssuranceUtrvStatusProps extends ColumnCommonProps {
  AssuranceUtrvs: UniversalTrackerValueAssurance[];
}

export const ColumnAssuranceUtrvStatus = (props: ColumnAssuranceUtrvStatusProps) => {
  const { question, AssuranceUtrvs } = props;

  if (!question.utrv) {
    return null;
  }

  const utrv = question.utrv;

  const getIcon = (utrv: Pick<SurveyModelMinimalUtrv, 'status' | 'assuranceStatus' | 'lastUpdated'>) => {
    if (!utrv) {
      return {
        icon: '',
        onClick: undefined,
        disabled: true,
        className: 'fixed-width',
        tooltip: undefined,
      };
    }

    const defaultIcon = {
      icon: getStatusIcon(utrv),
      onClick: undefined,
      disabled: true,
      className: 'fixed-width',
    };

    if (isUtrvAssuranceComplete(utrv.assuranceStatus)) {
      return {
        ...defaultIcon,
        className: 'assured fixed-width',
        disabled: false
      };
    }

    return {
      ...defaultIcon,
      disabled: false,
      className: `${utrv.status} fixed-width`,
    };
  };

  const statusIcon = getIcon(utrv);
  const assuranceUtrv = AssuranceUtrvs.find((assuranceUtrv) => assuranceUtrv.utrvId === question.utrv?._id);
  return (
    <Column
      className='text-center fs-4'
      disabled={statusIcon.disabled}
      onClick={statusIcon.onClick}
      tooltip={statusIcon.tooltip}
    >
      <QuestionStatus assuranceUtrv={assuranceUtrv} />
    </Column>
  );
};
