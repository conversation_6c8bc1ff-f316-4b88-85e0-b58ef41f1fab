/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { AssuranceDocumentType } from '../types/assurance';
import { QUESTION, SURVEY } from './terminology';

const mapping: Record<AssuranceDocumentType, string> = {
  basis_of_reporting: 'Basis of Reporting',
  management_statement: 'Management Statement',
  assurance_statement: 'Assurance Statement',
}

export const getAssuranceDocName = (type: AssuranceDocumentType) => {
  return mapping[type] ?? type;
}

export enum AssuranceRoles {
  Admin = 'admin',
  Assurer = 'user',
  RestrictedUser = 'restricted',
}

export const NO_ACCESS_MESSAGE = `To access ${SURVEY.SINGULAR} details you must either be the ${SURVEY.SINGULAR} admin or have ${SURVEY.SINGULAR} ${QUESTION.PLURAL} delegated to you. If you believe you should have access to this ${SURVEY.SINGULAR} please contact the ${SURVEY.SINGULAR} admin.`;

export const statusColumns = {
  notReady: {
    name: 'fal fa-award text-ThemeTextLight',
    text: 'Not ready for Assurance',
    accessor: 'notReady',
  },
  verified: {
    name: 'fal fa-award text-ThemeWarningMedium',
    text: 'Verified by company',
    accessor: 'isVerified',
  },
  rejected: {
    name: 'fal fa-award text-ThemeDangerMedium',
    text: 'Disputed',
    accessor: 'isRejected',
  },
  completed: {
    name: 'fal fa-award text-ThemeSuccessMedium',
    text: 'Assured',
    accessor: 'isCompleted',
  },
};
