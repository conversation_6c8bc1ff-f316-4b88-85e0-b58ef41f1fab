import { describe, it, expect, vi } from 'vitest';
import { getAdditionalContext } from './utils';
import { UtrValueType, TableColumnType } from '@g17eco/types/universalTracker';
import { createUtr, createTableValidation } from '@fixtures/utr/utrv-factory';

// Define types inline to avoid import restrictions
interface InputColumn {
  code: string;
  value?: string | number | string[];
  unit?: string;
  numberScale?: string;
}

interface TableColumn {
  code: string;
  name: string;
  type: TableColumnType;
}

enum RowStatus {
  removed = 'removed',
  edited = 'edited',
  added = 'added',
  original = 'original',
}

interface RowDataInfo {
  id: number;
  rowStatus: RowStatus;
  isRemoved?: boolean;
  isEdited?: boolean;
  hasChanged?: boolean;
  data: InputColumn[];
}

interface TableDataInfo {
  rows: RowDataInfo[];
  editRowId: number;
}

// Mock the convertInputData function
vi.mock('@utils/valueDataTable', () => ({
  convertInputData: vi.fn((rowData: InputColumn[][], _columnMap: any) => {
    // Simple mock implementation that returns the input data with some transformation
    return rowData.map((row: InputColumn[]) =>
      row.map((col: InputColumn) => ({
        ...col,
        value: col.value,
      }))
    );
  }),
}));

describe('getAdditionalContext', () => {
  const createTableColumn = (code: string, type: TableColumnType = TableColumnType.Text): TableColumn => ({
    code,
    name: `Column ${code}`,
    type,
  });

  const createInputColumn = (code: string, value?: string | number | string[]): InputColumn => ({
    code,
    value,
  });

  const createTableDataInfo = (rows: InputColumn[][] = [], editRowId: number = -1): TableDataInfo => ({
    rows: rows.map((row, index) => ({
      id: index,
      data: row,
      rowStatus: RowStatus.original,
      isRemoved: false,
    })),
    editRowId,
  });

  describe('when UTR valueType is Table', () => {
    it('should return additional context with input data for single row table', () => {
      const columns = [
        createTableColumn('col1', TableColumnType.Text),
        createTableColumn('col2', TableColumnType.Number),
      ];

      const utr = createUtr('test-table', {
        valueType: UtrValueType.Table,
        valueValidation: createTableValidation(columns),
      });

      const inputRow = [
        createInputColumn('col1', 'test value'),
        createInputColumn('col2', 123),
      ];

      const table = createTableDataInfo([inputRow]);

      const result = getAdditionalContext({ utr, table });

      expect(result).toEqual({
        additionalContext: {
          inputData: [
            { code: 'col1', value: 'test value' },
            { code: 'col2', value: 123 },
          ],
        },
      });
    });

    it('should return additional context with input data for multi-row table when editRowId is set', () => {
      const columns = [
        createTableColumn('col1', TableColumnType.Text),
        createTableColumn('col2', TableColumnType.Number),
      ];

      const utr = createUtr('test-table', {
        valueType: UtrValueType.Table,
        valueValidation: createTableValidation(columns, 5), // maxRows = 5 for multi-row
      });

      const inputRows = [
        [createInputColumn('col1', 'row 1'), createInputColumn('col2', 100)],
        [createInputColumn('col1', 'row 2'), createInputColumn('col2', 200)],
      ];

      const table = createTableDataInfo(inputRows, 1); // editing second row

      const result = getAdditionalContext({ utr, table });

      expect(result).toEqual({
        additionalContext: {
          inputData: [
            { code: 'col1', value: 'row 2' },
            { code: 'col2', value: 200 },
          ],
        },
      });
    });

    it('should use rowToAdd when provided for multi-row table', () => {
      const columns = [
        createTableColumn('col1', TableColumnType.Text),
        createTableColumn('col2', TableColumnType.Number),
      ];

      const utr = createUtr('test-table', {
        valueType: UtrValueType.Table,
        valueValidation: createTableValidation(columns, 5),
      });

      const table = createTableDataInfo([], -1); // no existing rows, not editing

      const rowToAdd = [
        createInputColumn('col1', 'new row'),
        createInputColumn('col2', 999),
      ];

      const result = getAdditionalContext({ utr, table, rowToAdd });

      expect(result).toEqual({
        additionalContext: {
          inputData: [
            { code: 'col1', value: 'new row' },
            { code: 'col2', value: 999 },
          ],
        },
      });
    });

    it('should handle array values by joining them with commas', () => {
      const columns = [
        createTableColumn('col1', TableColumnType.ValueListMulti),
        createTableColumn('col2', TableColumnType.Text),
      ];

      const utr = createUtr('test-table', {
        valueType: UtrValueType.Table,
        valueValidation: createTableValidation(columns),
      });

      const inputRow = [
        createInputColumn('col1', ['option1', 'option2', 'option3']),
        createInputColumn('col2', 'single value'),
      ];

      const table = createTableDataInfo([inputRow]);

      // Mock convertInputData to return array values
      const mockConvertInputData = require('@utils/valueDataTable').convertInputData;
      mockConvertInputData.mockReturnValueOnce([
        [
          { code: 'col1', value: ['option1', 'option2', 'option3'] },
          { code: 'col2', value: 'single value' },
        ],
      ]);

      const result = getAdditionalContext({ utr, table });

      expect(result).toEqual({
        additionalContext: {
          inputData: [
            { code: 'col1', value: 'option1, option2, option3' },
            { code: 'col2', value: 'single value' },
          ],
        },
      });
    });

    it('should return empty object when no current row is found', () => {
      const columns = [createTableColumn('col1')];

      const utr = createUtr('test-table', {
        valueType: UtrValueType.Table,
        valueValidation: createTableValidation(columns),
      });

      const table = createTableDataInfo([]); // no rows

      const result = getAdditionalContext({ utr, table });

      expect(result).toEqual({});
    });

    it('should handle missing column data gracefully', () => {
      const columns = [
        createTableColumn('col1'),
        createTableColumn('col2'),
        createTableColumn('col3'),
      ];

      const utr = createUtr('test-table', {
        valueType: UtrValueType.Table,
        valueValidation: createTableValidation(columns),
      });

      const inputRow = [
        createInputColumn('col1', 'value1'),
        // col2 is missing
        createInputColumn('col3', 'value3'),
      ];

      const table = createTableDataInfo([inputRow]);

      const result = getAdditionalContext({ utr, table });

      expect(result).toEqual({
        additionalContext: {
          inputData: [
            { code: 'col1', value: 'value1' },
            { code: 'col2', value: undefined },
            { code: 'col3', value: 'value3' },
          ],
        },
      });
    });

    it('should handle empty valueValidation.table.columns', () => {
      const utr = createUtr('test-table', {
        valueType: UtrValueType.Table,
        valueValidation: createTableValidation([]),
      });

      const table = createTableDataInfo([[]]);

      const result = getAdditionalContext({ utr, table });

      expect(result).toEqual({
        additionalContext: {
          inputData: [],
        },
      });
    });

    it('should handle missing valueValidation', () => {
      const utr = createUtr('test-table', {
        valueType: UtrValueType.Table,
        // no valueValidation
      });

      const table = createTableDataInfo([[]]);

      const result = getAdditionalContext({ utr, table });

      expect(result).toEqual({
        additionalContext: {
          inputData: [],
        },
      });
    });
  });

  describe('when UTR valueType is not Table', () => {
    it('should return empty object for Number valueType', () => {
      const utr = createUtr('test-number', {
        valueType: UtrValueType.Number,
      });

      const table = createTableDataInfo([]);

      const result = getAdditionalContext({ utr, table });

      expect(result).toEqual({});
    });

    it('should return empty object for Text valueType', () => {
      const utr = createUtr('test-text', {
        valueType: UtrValueType.Text,
      });

      const table = createTableDataInfo([]);

      const result = getAdditionalContext({ utr, table });

      expect(result).toEqual({});
    });

    it('should return empty object for Percentage valueType', () => {
      const utr = createUtr('test-percentage', {
        valueType: UtrValueType.Percentage,
      });

      const table = createTableDataInfo([]);

      const result = getAdditionalContext({ utr, table });

      expect(result).toEqual({});
    });

    it('should return empty object for ValueList valueType', () => {
      const utr = createUtr('test-valuelist', {
        valueType: UtrValueType.ValueList,
      });

      const table = createTableDataInfo([]);

      const result = getAdditionalContext({ utr, table });

      expect(result).toEqual({});
    });
  });
});