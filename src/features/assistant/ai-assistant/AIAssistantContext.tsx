import { useLazyGetAIUtrvAssistantQuery } from '@api/ai';
import { InputColumn } from '@components/survey/form/input/table/InputInterface';
import { AnswerDataProps } from '@features/assistant/ai-assistant/Answer';
import { getAdditionalContext } from '@features/assistant/ai-assistant/utils';
import { AIResponse } from '@g17eco/types/ai';
import { createContext, useContext } from 'react';

interface AIAssistantContextProps {
  getTextDraft: (columnCode?: string, rowToAdd?: InputColumn[]) => Promise<Pick<AIResponse, 'content'>>;
}

export const AIAssistantContext = createContext<AIAssistantContextProps>({
  getTextDraft: async () => Promise.resolve({ content: '' }),
});

export const useAIAssistantContext = () => {
  return useContext(AIAssistantContext);
};

interface Props {
  children: JSX.Element;
  initiativeId: string;
  utrvId: string | undefined;
  utr: AnswerDataProps['utr'] | undefined;
}

export const AIAssistantContextProvider = ({ children, initiativeId, utrvId, utr }: Props) => {
  const [getAIUtrvAssistant] = useLazyGetAIUtrvAssistantQuery();

  const getTextDraft = async (columnCode?: string, currentRow?: InputColumn[]) => {
    if (!utrvId || !utr) {
      return { content: '' };
    }

    return getAIUtrvAssistant({ initiativeId, utrvId, additionalContext: getAdditionalContext({ utr, currentRow }) })
      .unwrap()
      .then((response) => {
        const predictAnswer =
          columnCode && typeof response.predictedAnswer === 'object'
            ? response.predictedAnswer?.[columnCode]
            : response.predictedAnswer;

        return { content: typeof predictAnswer === 'string' ? predictAnswer : JSON.stringify(predictAnswer) };
      });
  };

  return <AIAssistantContext.Provider value={{ getTextDraft }}>{children}</AIAssistantContext.Provider>;
};
