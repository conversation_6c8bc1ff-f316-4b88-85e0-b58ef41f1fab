/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { createSelector } from '@reduxjs/toolkit';
import { getSDGShortTitle, isHidden, sdgMap } from '../constants/sdg-data';
import { frameworks, Standards, standards, getGroup, type Group } from '@g17eco/core';
import {
  Company,
  Scope,
  ScopeGroups,
  SurveyActionData,
  SurveyGroupsData,
  SurveyModelMinimalUtrv
} from '../model/surveyData';
import UniversalTracker from '../model/UniversalTracker';
import {
  getQuestionName,
  getQuestionShortPrefix
} from '../utils/universalTracker';
import { PERMISSION_GROUPS } from '../utils/permission-groups';
import {
  filterFramework,
  filterMateriality,
  filterSdg,
  filterStandard,
  filterStandardAndFramework,
} from '../components/survey/utils/filters';
import { Breadcrumb, ScopeQuestion, ScopeQuestionOptionalValue } from '../types/surveyScope';
import { ViewMap, ViewValues } from '../components/survey-overview-sidebar/viewOptions';
import { getScopeInfoByMap } from '../components/survey-scope/scopeSelection';
import { naturalSort } from '../utils';
import {
  ScopeQuestionGroup,
  ScopeQuestionGroupOptionalValue,
  ScopeGroup,
  ScopeCategoryGroups,
  QuestionList,
  BaseQuestionGroup,
  QuestionSubGroup,
} from '../types/survey';
import { RootState } from '../reducers';
import { AccessType, MetricGroup } from '../types/metricGroup';
import { findMatchingConfig, sortItemsByTypeCode, sortItemsByUtrSortingOrder } from '../utils/sort';
import { Materiality } from '../types/initiative';
import { convertSurveyMaterialityToUtrCodeMap, getSurveyGroups, getSurveyQuestionProgress } from '../utils/survey';
import { InitiativeUniversalTracker } from '../types/initiativeUniversalTracker';
import { endpoints } from '../api/initiative-universal-trackers';
import { filterMetricGroups, sortCustomGroupQuestions } from '@utils/metricGroup';
import { getSrc } from '@components/sdg-icon/utils';

interface TagScopeGroup<T extends ScopeQuestionOptionalValue = ScopeQuestion> {
  [key: keyof Standards]: ScopeGroup<T>
}

type MetricGroupMin = Pick<
  MetricGroup,
  'universalTrackers' | 'metricsOrder' | 'groupName' | 'initiative' | 'initiativeId' | '_id' | 'groupData' | 'accessType'
>;

const findUTrvByUtrId = (survey: Pick<SurveyGroupsData, 'fragmentUniversalTrackerValues'>, utrId: string, initiativeId: string) => {
  if (!survey.fragmentUniversalTrackerValues) {
    return;
  }

  const find = (utrv: Pick<SurveyModelMinimalUtrv, 'universalTrackerId' | 'initiativeId'>) => utrv.universalTrackerId === utrId && utrv.initiativeId === initiativeId;
  return survey.fragmentUniversalTrackerValues.find(find);
};

const createQuestionOptionalValue = (
  survey: SurveyGroupsData,
  universalTracker: UniversalTracker,
  initiativeId: string
): ScopeQuestionOptionalValue => ({
  utrv: findUTrvByUtrId(survey, universalTracker.getId(), initiativeId),
  universalTracker: universalTracker,
  frameworkCode: universalTracker.getFrameworkCode(),
  valueType: universalTracker.getValueType(),
  valueValidation: universalTracker.getValueValidation(),
  shortPrefix: getQuestionShortPrefix(universalTracker),
  name: getQuestionName(universalTracker),
});

export const createScopeQuestionGroupOptionalValue = (survey: SurveyGroupsData) => {
  const questionGroupList: ScopeQuestionGroupOptionalValue[] = [];
  survey.questionGroups.forEach((questionGroup) => {
    const questionList: ScopeQuestionOptionalValue[] = [];
    questionGroup.questions.forEach(({ utr }) => {
      const universalTracker = new UniversalTracker(utr);
      questionList.push(
        createQuestionOptionalValue(survey, universalTracker, survey.initiativeId)
      );
    });

    if ('groupData' in questionGroup) { // Custom Metric groups. Not reliable, but OK for now
      questionList.sort((a, b) => naturalSort(a.name, b.name));
    }

    questionGroupList.push({
      groupName: questionGroup.groupName,
      groupData: questionGroup.groupData,
      list: questionList
    });
  });
  return questionGroupList;
};

const createQuestion = (
  survey: Pick<SurveyActionData, 'fragmentUniversalTrackerValues'>,
  universalTracker: UniversalTracker,
  initiativeId: string,
): ScopeQuestion | undefined => {
  const utrv = findUTrvByUtrId(survey, universalTracker.getId(), initiativeId);
  if (!utrv) {
    return;
  }

  const questionName = getQuestionName(universalTracker);
  return {
    utrv: utrv,
    universalTracker: universalTracker,
    frameworkCode: universalTracker.getFrameworkCode(),
    valueType: universalTracker.getValueType(),
    valueValidation: universalTracker.getValueValidation(),
    shortPrefix: getQuestionShortPrefix(universalTracker),
    name: questionName,
    sortTitle: questionName
  };
};

export const createScopeQuestionGroup = (
  survey: Pick<SurveyActionData, 'questionGroups' | 'initiativeId' | 'fragmentUniversalTrackerValues'>,
  initiativeUniversalTrackers?: InitiativeUniversalTracker[]
): ScopeQuestionGroup[] => {
  const questionGroupList: ScopeQuestionGroup[] = [];
  survey.questionGroups.forEach((questionGroup) => {
    const questionList: ScopeQuestion[] = [];
    questionGroup.questions.forEach(({ utr }) => {
      const universalTracker = new UniversalTracker(utr);

      // override initiative universal trackers
      if (initiativeUniversalTrackers && initiativeUniversalTrackers.length > 0) {
        overrideInitiativeUtr({ initiativeUniversalTrackers, utr: universalTracker });
      }

      const question = createQuestion(survey, universalTracker, survey.initiativeId);
      if (!question) {
        return;
      }
      questionList.push(question);
    });

    if ('groupData' in questionGroup) { // Custom Metric groups. Not reliable, but OK for now
      questionList.sort((a, b) => naturalSort(a.name, b.name));
    }

    questionGroupList.push({
      groupName: questionGroup.groupName,
      groupData: questionGroup.groupData,
      list: questionList
    });
  });
  return questionGroupList;
};

const getSurveyGroupsByBlueprint = createSelector(
  [
    (state: RootState) => state.survey,
  ],
  (surveyState) => {
    if (!surveyState.loaded) {
      return [];
    }
    return createScopeQuestionGroup(surveyState.data);
  }
);


export function convertToQuestionMapByCode(scopeGroups: ScopeQuestionGroup[]) {
  const questionMap = new Map<string, ScopeQuestion>();
  scopeGroups.forEach(questionGroup => {
    questionGroup.list.forEach(question => {
      questionMap.set(question.universalTracker.getCode(), question);
    });
  });
  return questionMap;
}

function convertToQuestionMapByUtrvId(scopeGroups: ScopeQuestionGroup[]) {
  const questionMap = new Map<string, ScopeQuestion>();
  if (!scopeGroups) {
    return questionMap;
  }
  scopeGroups.forEach(questionGroup => {
    questionGroup.list.forEach(question => {
      questionMap.set(question.utrv._id, question);
    });
  });
  return questionMap;
}

function convertToQuestionMapByUtrId(scopeGroups: ScopeQuestionGroup[]) {
  const questionMap = new Map<string, ScopeQuestion>();
  if (!scopeGroups) {
    return questionMap;
  }
  scopeGroups.forEach(questionGroup => {
    questionGroup.list.forEach(question => {
      questionMap.set(question.universalTracker.getId(), question);
    });
  });
  return questionMap;
}

export const convertToQuestionList = (scopeGroups: ScopeQuestionGroup[]) => {
  const questionMap = convertToQuestionMapByUtrvId(scopeGroups);
  return Array.from(questionMap, ([k, v]) => v);
}

export const getQuestionProgress = createSelector(
  [getSurveyGroupsByBlueprint],
  (questionGroupList) => {
    return getSurveyQuestionProgress(questionGroupList);
  }
)

export function initializeScopeGroupStandards<T extends ScopeQuestionOptionalValue = ScopeQuestion>() {
  const tags: TagScopeGroup<T> = {};
  for (const [tag, standard] of Object.entries(standards)) {
    tags[tag] = {
      group: {
        title: standard.name,
        subtitle: standard.subtitle,
        description: standard.description ?? '',
        src: standard.src,
        link: standard.link
      },
      list: []
    };
  }
  return tags;
}

export function initializeScopeGroupSDG<T extends ScopeQuestionOptionalValue = ScopeQuestion>() {
  const tags: TagScopeGroup<T> = {};
  sdgMap.forEach(goal => {
    const shortTitle = getSDGShortTitle(goal.code);
    const tag = goal.code;
    tags[tag] = {
      group: {
        title: `Goal ${goal.code} ${shortTitle}`,
        subtitle: goal.shortName,
      },
      list: []
    };
  });
  return tags;
}

export function initializeScopeGroupFrameworkSubGroups<T extends ScopeQuestionOptionalValue = ScopeQuestion>() {
  const tags: TagScopeGroup<T> = {};
  Object.keys(frameworks).forEach((code) => {
    const frameworkSubtypes = frameworks[code];
    return frameworkSubtypes.subgroups?.forEach((framework) => {
      const tag = framework.code;

      tags[tag] = {
        group: {
          title: framework.name,
          subtitle: framework.subtitle,
          description: framework.description,
          src: framework.src,
          colour: framework.colour
        },
        list: []
      }
    });
  });
  return tags;
}

export function initializeScopeGroupFrameworks<T extends ScopeQuestionOptionalValue = ScopeQuestion>() {
  const tags: TagScopeGroup<T> = {};
  Object.keys(frameworks).forEach((code) => {
    const framework = frameworks[code];
    tags[code] = {
      group: {
        title: framework.name,
        subtitle: framework.subtitle,
        description: framework.description,
        src: framework.src,
        colour: framework.colour
      },
      list: []
    }
  });
  return tags;
}

export function initializeScopeGroupCustomMetrics<T extends ScopeQuestionOptionalValue = ScopeQuestion>(surveyData: SurveyGroupsData) {
  const surveyInitiativeId = surveyData.initiativeId;

  const getGroupTitle = (metricGroup: Pick<MetricGroup, 'groupName' | 'initiative' | 'initiativeId' | 'accessType'>) => {
    const isInherited = metricGroup.accessType === AccessType.Inherited;
    if (surveyInitiativeId === metricGroup.initiativeId || !metricGroup.initiative) {
      return metricGroup.groupName;
    }
    return `${isInherited ? metricGroup.groupName : `${metricGroup.initiative.name} assigned: ${metricGroup.groupName}`}`
  }

  const tags: TagScopeGroup<T> = {};
  surveyData.customMetricGroups.forEach((metricGroup: MetricGroupMin) => {
    const metricGroupColour = metricGroup.groupData?.colour ?? '#e6e8ed';
    const tag = metricGroup._id;

    tags[tag] = {
      group: {
        title: getGroupTitle(metricGroup),
        icon: metricGroup.groupData?.icon,
        colour: metricGroupColour,
        link: metricGroup.groupData?.link,
        preferredAltCodes: metricGroup.groupData?.preferredAltCodes,
        metricsOrder: metricGroup.metricsOrder,
        universalTrackerIds: metricGroup.universalTrackers,
        isInherited: metricGroup.accessType === AccessType.Inherited,
      },
      list: []
    };
  });
  return tags;
}

function initializeSubGroup<T extends ScopeQuestionOptionalValue = ScopeQuestion>(group: Group, depth = 1, maxDepth = 3) {
  const result: QuestionSubGroup<T> = {
    code: group.code,
    name: group.name,
    list: [],
    subGroups: undefined,
  };

  if (depth < maxDepth && group.subgroups?.length) {
    result.subGroups = group.subgroups.map((sub) => initializeSubGroup(sub, depth + 1, maxDepth));
  }

  return result;
}

function getSubGroupsByScopeKey<T extends ScopeQuestionOptionalValue = ScopeQuestion>(scopeKey: ScopeGroups, scopeTag: string): QuestionSubGroup<T>[] | undefined {
  switch (scopeKey) {
    case ScopeGroups.Standards:
    case ScopeGroups.Frameworks: {
      const subgroups = getGroup('standards-and-frameworks', scopeTag)?.subgroups;
      return subgroups?.map((group) => initializeSubGroup(group));
    }
    default:
      return;
  }
}

function getQuestionsBySubGroup<T extends ScopeQuestionOptionalValue = ScopeQuestion>(
  surveyData: SurveyGroupsData,
  questions: T[],
  scopeKey: ScopeGroups,
  scopeTag: string,
  isShowingMappedMetrics: boolean,
) {
  const subGroups = getSubGroupsByScopeKey<T>(scopeKey, scopeTag);
  if (!subGroups) {
    return;
  }

  const uniqueQuestions = new Set();

  const breadcrumbs: Breadcrumb[] = [
    { cardCategory: scopeTag, cardGroup: scopeKey.toString() as ViewValues, title: '' },
  ];
  const isSdgTag = scopeTag === ScopeGroups.Sdg;

  const filterAndSortQuestions = (subGroup: QuestionList<T>) => {
    const filteredQuestionList = questions.filter((q) =>
      isSdgTag ? filterSdg(q, surveyData.contributions, subGroup.code) : filterStandardAndFramework(q, subGroup.code),
    );
    filteredQuestionList.forEach((q) => uniqueQuestions.add(q.universalTracker.getId()));
    // utrs sorting at sub group level
    const order = findMatchingConfig(surveyData.utrSortingConfigMap?.[scopeTag], subGroup.code)?.order;
    return order ? sortItemsByUtrSortingOrder(filteredQuestionList, order) : filteredQuestionList;
  };

  subGroups.forEach((subGroup) => {
    if (isSdgTag && isHidden(subGroup.code)) {
      return;
    }
    const { inScope, isPartial } = getScopeInfoByMap(
      surveyData.scope,
      subGroup.code,
      scopeKey,
      breadcrumbs,
      surveyData.initiatives[0].materialityMap
    );

    if (!isShowingMappedMetrics && !inScope && !isPartial) {
      return;
    }
    subGroup.list = filterAndSortQuestions(subGroup);
    const queue = subGroup.subGroups ? [...subGroup.subGroups] : [];
    while (queue.length > 0) {
      const group = queue.shift();
      if (group) {
        group.list = filterAndSortQuestions(group);
        if (group.subGroups) {
          queue.push(...group.subGroups);
        }
      }
    }
  });
  
  const rootLevelQuestions = questions.filter(q => !uniqueQuestions.has(q.universalTracker.getId()));

  // Only if the main tag is added, we should create virtual other group,
  // to ensure typeTags that are missing are still showing up in this virtual group,
  // otherwise everything is based off subGroups explicitly selected
  const shouldCreateOtherGroup = rootLevelQuestions.length > 0 && getScopeInfoByMap(
    surveyData.scope,
    scopeTag,
    scopeKey,
    breadcrumbs,
    surveyData.initiatives[0].materialityMap
  ).inScope;

  if (shouldCreateOtherGroup) {
    subGroups.unshift({
      code: '',
      name: 'Other',
      list: rootLevelQuestions
    });
  }
  return subGroups;
}

export function scopeGroupProcessor<T extends ScopeQuestionOptionalValue = ScopeQuestion>(
  surveyData: SurveyGroupsData,
  questionList: T[],
  emptyScopeGroups: ScopeCategoryGroups<T>
): ScopeCategoryGroups<T> {

  const scopeGroups: ScopeCategoryGroups<T> = JSON.parse(JSON.stringify(emptyScopeGroups));
  const questionGroups = surveyData.questionGroups.filter(group => group.groupId).map(group => ({
    _id: group.groupId as string,
    universalTrackers: group.questions.map(question => question.utr._id),
  }));

  const getFilterFunction = (scopeKey: string, question: ScopeQuestionOptionalValue) => {
    switch (scopeKey) {
      case ScopeGroups.Sdg:
        return (scopeTag: string) => filterSdg(question, surveyData.contributions, scopeTag);
      case ScopeGroups.Standards:
        return (scopeTag: string) => filterStandard(question, scopeTag);
      case ScopeGroups.Frameworks:
        return (scopeTag: string) => filterFramework(question, scopeTag);
      case ScopeGroups.Materiality: {
        const materialityMap = convertSurveyMaterialityToUtrCodeMap(surveyData.initiatives[0]?.materialityMap);
        return (scopeTag: string) =>
          materialityMap
            ? filterMateriality(question, scopeTag as Materiality, materialityMap, surveyData.contributions)
            : undefined;
      }
      case ScopeGroups.Custom:
        return (scopeTag: string) =>
          filterMetricGroups({
            utrId: question.universalTracker.getId(),
            metricGroupId: scopeTag,
            metricGroups: questionGroups,
          });
      default:
        return;
    }
  }

  const getSortName = (q: T) => `${q.shortPrefix} ${q.name}`.trim();
  const sortedQuestions = questionList.sort((a, b) => getSortName(a).localeCompare(getSortName(b), navigator.languages[0] || navigator.language, {
    numeric: true,
    ignorePunctuation: true
  }));

  const scopeKeys = Object.keys(scopeGroups) as ScopeGroups[];
  sortedQuestions.forEach(question => {
    scopeKeys.forEach(scopeKey => {
      const isInScope = getFilterFunction(scopeKey, question);
      const scopeTags = Object.keys(scopeGroups[scopeKey]);
      scopeTags.forEach(scopeTag => {
        if (Array.isArray(scopeGroups[scopeKey][scopeTag]?.list) && isInScope?.(scopeTag)) {
          scopeGroups[scopeKey][scopeTag].list.push(question);
        }
      });
    });
  });
  return scopeGroups;
}

const getQuestionIdentifier = <T extends ScopeQuestionOptionalValue>(q: T) => q.utrv?._id ?? q.universalTracker.getId();

/**
 * Only returns questions that appear in subgroups, while preserving the order from original questions.
 */
function getListFromSubGroups<T extends ScopeQuestionOptionalValue = ScopeQuestion>(
  sortedQuestions: T[],
  subGroups: QuestionList<T>[] | undefined,
): T[] {
  if (!subGroups) {
    return sortedQuestions;
  }

  const subGroupQuestionsMap = subGroups.reduce((acc, subGroup) => {
    subGroup.list.forEach((q) => acc.set(getQuestionIdentifier(q), q));
    return acc;
  }, new Map<string, T>());

  // Using the subgroup versions of the questions
  return sortedQuestions.reduce((acc: T[], q) => {
    const id = getQuestionIdentifier(q);
    const subGroupQuestion = subGroupQuestionsMap.get(id);
    if (subGroupQuestion) {
      acc.push(subGroupQuestion);
    }
    return acc;
  }, []);
}

export function surveyGroupsByScopeGroupsProcessor<T extends ScopeQuestionOptionalValue = ScopeQuestion>(
  surveyData: SurveyGroupsData<T>,
  scopeGroups: ScopeCategoryGroups<T>,
  isShowingMappedMetrics: boolean = false,
) {
  const surveyGroups: BaseQuestionGroup<T>[] = [];
  const materialityMap = surveyData.initiatives[0].materialityMap;
  const scopeKeys = Object.keys(scopeGroups) as ScopeGroups[];
  scopeKeys.forEach(scopeKey => {
    const scopeTags = Object.keys(scopeGroups[scopeKey]);
    const newGroups: BaseQuestionGroup<T>[] = [];
    scopeTags.forEach(scopeTag => {
      const { inScope, isPartial } = getScopeInfoByMap(surveyData.scope, scopeTag, scopeKey, [], materialityMap);
      if (!isShowingMappedMetrics && !inScope && !isPartial) {
        return;
      }

      const tagGroup = scopeGroups[scopeKey][scopeTag];
      let questions = tagGroup.list;
      if (scopeKey === 'custom') {
        questions = sortCustomGroupQuestions(questions, tagGroup.group);
      } else {
        // utrs sorting at group level
        const order = findMatchingConfig(surveyData.utrSortingConfigMap?.[scopeTag], scopeTag)?.order;
        questions = order ? sortItemsByUtrSortingOrder(questions, order) : sortItemsByTypeCode(questions, scopeTag);
      }

      const alternativeCode = standards[scopeTag] ? scopeTag : undefined;
      const subGroupsWithList = getQuestionsBySubGroup(surveyData, questions, scopeKey, scopeTag, isShowingMappedMetrics);

      newGroups.push({
        groupName: tagGroup.group.title,
        groupCode: scopeTag,
        groupData: {
          colour: tagGroup.group.colour,
          icon: tagGroup.group.icon,
          preferredAltCodes: tagGroup.group.preferredAltCodes,
          alternativeCode,
          isInherited: tagGroup.group.isInherited,
        },
        alwaysVisible: scopeKey === 'custom', // Only force custom metrics
        subGroups: subGroupsWithList,
        list: getListFromSubGroups(questions, subGroupsWithList),
      });
    });
    if (newGroups.length > 0) {
      if (scopeKey === 'custom') {
        newGroups.sort((a, b) => naturalSort(a.groupName, b.groupName));
      }
      surveyGroups.push({
        groupName: ViewMap[scopeKey].label ?? scopeKey,
        groupData: {
          colour: '#1a4791'
        },
        list: [],
        count: newGroups.reduce((count, g) => count + g.list.length, 0),
        alwaysVisible: true
      });
      newGroups.forEach(g => surveyGroups.push(g));
    }
  });

  return surveyGroups;
}

export function surveyGroupsByScopeSubGroupsProcessor<T extends ScopeQuestionOptionalValue = ScopeQuestion>(
  surveyData: SurveyGroupsData,
  scopeGroups: ScopeCategoryGroups<T>
) {
  const surveyGroups: BaseQuestionGroup<T>[] = [];
  const materialityMap = surveyData.initiatives[0].materialityMap;
  const scopeKeys = Object.keys(scopeGroups) as ScopeGroups[];
  scopeKeys.forEach(scopeKey => {
    const scopeTags = Object.keys(scopeGroups[scopeKey]);
    const newGroups: BaseQuestionGroup<T>[] = [];
    scopeTags.forEach(scopeTag => {
      const { inScope, isPartial } = getScopeInfoByMap(surveyData.scope, scopeTag, scopeKey, [], materialityMap);
      if (!inScope && !isPartial) {
        return;
      }

      const tagGroup = scopeGroups[scopeKey][scopeTag];
      let questions = tagGroup.list;
      if (scopeKey === 'custom') {
        questions = sortCustomGroupQuestions(questions, tagGroup.group);
      }
      const alternativeCode = standards[scopeTag] ? scopeTag : undefined;
      newGroups.push({
        groupName: tagGroup.group.title,
        groupCode: scopeTag,
        groupData: {
          colour: tagGroup.group.colour,
          icon: tagGroup.group.icon,
          preferredAltCodes: tagGroup.group.preferredAltCodes,
          alternativeCode: alternativeCode ?? tagGroup.group.preferredAltCodes?.[0],
        },
        alwaysVisible: scopeKey === 'custom', // Only force custom metrics
        list: questions
      });
    });
    if (newGroups.length > 0) {
      if (scopeKey === 'custom') {
        newGroups.sort((a, b) => naturalSort(a.groupName, b.groupName));
      }
      surveyGroups.push({
        groupName: ViewMap[scopeKey].label ?? scopeKey,
        groupData: {
          colour: '#1a4791'
        },
        list: [],
        count: newGroups.reduce((count, g) => count + g.list.length, 0),
        alwaysVisible: true
      });
      newGroups.forEach(g => surveyGroups.push(g));
    }
  });

  return surveyGroups;
}

export function surveyGroupsBySDGAndScopeProcessor<T extends ScopeQuestionOptionalValue = ScopeQuestion>(
  surveyData: SurveyGroupsData,
  scopeGroups: ScopeCategoryGroups<T>
) {
  const surveyGroups: BaseQuestionGroup<T>[] = [];
  const materialityMap = surveyData.initiatives[0].materialityMap;
  const scope: Partial<Scope> = surveyData.scope ? { ...surveyData.scope } : {};
  scope.sdg = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17'];

  const displayedUTRVIds = new Set<string>();
  const scopeKeys = Object.keys(scopeGroups) as ScopeGroups[];
  scopeKeys.forEach(scopeKey => {
    const scopeTags = Object.keys(scopeGroups[scopeKey]);

    const newGroups: BaseQuestionGroup<T>[] = [];
    scopeTags.forEach(scopeTag => {
      const { inScope, isPartial } = getScopeInfoByMap(scope, scopeTag, scopeKey, [], materialityMap);
      if (!inScope && !isPartial) {
        return;
      }

      const tagGroup = scopeGroups[scopeKey][scopeTag];

      let questions: T[] = [];
      if (scopeKey === 'sdg') {
        // Keep track of questions added to SDG groups, but allow duplicates across sdg goals
        tagGroup.list.forEach(q => displayedUTRVIds.add(q.universalTracker.getId()));
        questions = [...tagGroup.list];
      } else {
        // For standards, don't display questions already in sdg group, but allow duplicates across standards
        questions = tagGroup.list.filter(q => !displayedUTRVIds.has(q.universalTracker.getId()));
        // Still show empty custom metric group
        if (questions.length === 0 && 'custom' !== scopeKey) {
          return;
        }
      }

      if (scopeKey === 'custom') {
        questions = sortCustomGroupQuestions(questions, tagGroup.group);
      }

      const alternativeCode = standards[scopeTag] ? scopeTag : undefined;
      newGroups.push({
        groupName: tagGroup.group.title,
        groupCode: scopeTag,
        groupData: {
          colour: tagGroup.group.colour,
          preferredAltCodes: tagGroup.group.preferredAltCodes,
          icon: scopeKey === 'sdg' ? getSrc({ code: scopeTag }) : tagGroup.group.icon,
          alternativeCode,
          isInherited: tagGroup.group.isInherited,
        },
        alwaysVisible: ['sdg', 'custom'].includes(scopeKey), // Only force SDG and custom scope to be always visible
        list: questions
      });
    });

    if (newGroups.length > 0) {
      if (scopeKey === 'custom') {
        newGroups.sort((a, b) => naturalSort(a.groupName, b.groupName));
      }
      surveyGroups.push({
        groupName: ViewMap[scopeKey].label ?? scopeKey,
        groupData: {
          colour: '#1a4791'
        },
        list: [],
        count: newGroups.reduce((count, g) => count + g.list.length, 0),
        alwaysVisible: true
      });
      newGroups.forEach(g => surveyGroups.push(g));
    }
  });

  return surveyGroups;
}

export const createGetInitiativeUniversalTrackersSelector = createSelector(
  (id: string) => id,
  (id) => {
    return endpoints.getInitiativeUniversalTrackers.select(id);
  }
)

export const getInitiativeUniversalTrackersSelector = createSelector(
  (state: RootState) => state,
  (state: RootState, id: string) => createGetInitiativeUniversalTrackersSelector(id),
  (state, selectGetInitiativeUniversalTrackers) => {
    return selectGetInitiativeUniversalTrackers(state as any).data;
  },
)


export const getSurveyGroupsSelector = createSelector(
  [
    (state: RootState) => state.surveySettings.overviewMode,
    (state: RootState) => state.surveySettings.isShowingMappedMetrics,
    (state: RootState) => state.survey,
    (state: RootState) => getInitiativeUniversalTrackersSelector(state, state.survey.loaded ? state.survey.data.initiativeId : ''),
  ],
  (overviewMode, isShowingMappedMetrics, surveyState, initiativeUniversalTrackers) => {
    const surveyData = surveyState.loaded ? surveyState.data : undefined;
    if (!surveyData) {
      return [];
    }
    const blueprintGroups = createScopeQuestionGroup(surveyData, initiativeUniversalTrackers);
    const questionList = convertToQuestionList(blueprintGroups);
    return getSurveyGroups(overviewMode, surveyData, blueprintGroups, questionList, isShowingMappedMetrics);
  }
);

export const getQuestionListMap = createSelector(
  [getSurveyGroupsByBlueprint],
  (surveyGroups) => {
    return convertToQuestionMapByCode(surveyGroups);
  }
);

export const getQuestionOptionalValueListMap = createSelector(
  [(state: RootState) => state.survey],
  (surveyState) => {
    const questionList = new Map<string, ScopeQuestionOptionalValue>();
    if (!surveyState.loaded) {
      return questionList;
    }

    const questionGroupList = createScopeQuestionGroupOptionalValue(surveyState.data);
    if (!questionGroupList) {
      return questionList;
    }

    questionGroupList.forEach(questionGroup => {
      questionGroup.list.forEach(question => {
        questionList.set(question.universalTracker.getCode(), question);
      });
    });
    return questionList;
  }
);

export const getUtrFromSurvey = ({ survey, surveyGroups, questionId }:{
    survey?: SurveyGroupsData,
    surveyGroups: ScopeQuestionGroup[],
    questionId: string,
  }): {
    utr?: UniversalTracker,
    utrv?: SurveyModelMinimalUtrv
  } => {
  if (!survey) {
    return {};
  }

  const questionList = convertToQuestionMapByUtrId(surveyGroups);

  const findData = () => {
    const utrv = survey.fragmentUniversalTrackerValues?.find((utrv) => utrv._id === questionId);
    if (!utrv) {
      return {}
    }
    const utrId = utrv.universalTrackerId;
    const utr = questionList.get(utrId)?.universalTracker;

    if (!utr) {
      return {};
    }

    return {
      utrv,
      utr,
    }
  }

  const questionDetails = findData();
  if (!questionDetails) {
    return {};
  }

  return questionDetails;
}

const overrideInitiativeUtr = ({
  initiativeUniversalTrackers,
  utr,
}: {
  initiativeUniversalTrackers: InitiativeUniversalTracker[],
  utr: UniversalTracker,
}) => {
  const initiativeUtr = initiativeUniversalTrackers.find((item) => item.universalTrackerId === utr.getId());
  if (initiativeUtr) {
    utr.setMetricOverrides(initiativeUtr);
  }
}

export const getQuestionId = (questionIds: string[], currentId: string, currentIndex: number | string, isNext = false) => {

  const numericIndex = Number(currentIndex);
  const idCount = questionIds.length;
  const lastIndex = idCount - 1

  // Index match current id,
  let index = questionIds[numericIndex] === currentId ? numericIndex : -1;
  if (index === -1) {
    for (let i = 0; i < lastIndex; i++) {
      if (questionIds[i] === currentId) {
        index = i;
        break;
      }
    }
  }

  if (index === -1) {
    const nextQuestionIndex = isNext ? numericIndex : numericIndex - 1;
    return { id: questionIds[nextQuestionIndex] ?? questionIds[0] ?? currentId, index: nextQuestionIndex };
  }

  const nextIndex = isNext ? index + 1 : index - 1;

  // Go to beginning
  if (lastIndex < nextIndex) {
    return { id: questionIds[0] ?? currentId, index: 0 };
  }

  // Go to the end
  if (nextIndex < 0) {
    return { id: questionIds[lastIndex] ?? currentId, index: lastIndex };
  }

  return { id: questionIds[nextIndex] ?? currentId, index: nextIndex };
}

export const getPermissionGroup = createSelector(
  [
    (state: RootState) => state.globalData,
  ],
  (globalData) => {
    const getGroup = (d?: Company, currentPermissionGroup?: string) => {
      const isNonFreeGroup = d?.permissionGroup && d?.permissionGroup !== PERMISSION_GROUPS.FREE;
      return isNonFreeGroup ? d?.permissionGroup : currentPermissionGroup;
    }

    const rootOrg = globalData.loaded ? globalData.data.organization : undefined
    return getGroup(rootOrg, PERMISSION_GROUPS.FREE);
  });
