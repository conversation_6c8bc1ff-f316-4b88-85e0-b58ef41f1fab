// @ts-check

import { fixupPluginRules } from '@eslint/compat';
import filenames from 'eslint-plugin-filenames';
import react from 'eslint-plugin-react';
import reactHooks from 'eslint-plugin-react-hooks';
import testingLibrary from 'eslint-plugin-testing-library';
import globals from 'globals';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import eslintJs from '@eslint/js'
import eslintTs from 'typescript-eslint'
import importPlugin from 'eslint-plugin-import'
import oxlint from 'eslint-plugin-oxlint';


// Add the files for applying the recommended TypeScript configs
// only for the Typescript files.
// This is necessary when we have the multiple extensions files
// (e.g. .ts, .tsx, .js, .cjs, .mjs, etc.).
const tsFiles = ['{src,tests}/**/*.ts(x)?', 'scripts/**/*.ts'];
const defaultFiles = ['**/*.{js,mjs,cjs,jsx,mjsx,ts,tsx,mtsx}'];

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const configArray = eslintTs.config(
  {
    name: 'Apply global ignore',
    // https://eslint.org/docs/latest/use/configure/ignore
    //  if an ignores key is used without any other keys in the configuration object, then the patterns act as global ignores
    ignores: [
      // only ignore node_modules in the same directory
      // as the configuration file
      'node_modules',
      'deploy',
      // so you have to add `**/` pattern to include nested directories
      // for example, if you use pnpm workspace
      '**/node_modules',
      'dist',
      'build',
      'docs',
      'lib',
      'coverage',
      'reports',
      'vite-env.d.ts',
      'vite.config.ts',
      'globalSetup.ts',
      '**/*.scss',
      '**/.DS_Store',
      '**/.bitbucket',
      '.editorconfig',
      '.eslintrc.cjs',
      '.env',
      '.env.*',
      '.gitignore',
      './*',
    ],
  },
  {
    name: 'Apply global settings and options',
    languageOptions: {
      ...react.configs.flat.recommended.languageOptions,
      globals: {
        ...globals.browser,
        ...globals.node,
        ...globals.vitest,
        JSX: "readonly",
        React: "readonly",
      },
    },
    settings: {
      react: {
        version: '18.3.1',
        defaultVersion: '18.3.1',
      },
      'import/parsers': {
        '@typescript-eslint/parser': ['.ts', '.tsx'],
      },
      'import/resolver': {
        typescript: true,
      },
    },
  },
  {
    name: 'JSX runtime',
    files: defaultFiles,
    ...react.configs.flat['jsx-runtime'],
  },
  {
    name: 'React runtime',
    files: defaultFiles,
    ...react.configs.flat.recommended,
  },
  {
    name: 'Default ESLint Recommended',
    ...eslintJs.configs.recommended,
  },
  ...eslintTs.configs.recommended.map((config) => ({
    ...config,
    files: tsFiles,
  })),
  ...eslintTs.configs.recommendedTypeChecked.map((config) => ({
    ...config,
    files: tsFiles,
  })),
  {
    name: 'React-Testing-Library',
    files: ['src/**/*.spec.ts(x)?'],
    ...testingLibrary.configs['flat/react'],
    rules: {
      ...testingLibrary.configs['flat/react'].rules,
      'testing-library/no-manual-cleanup': 'warn',
      'testing-library/prefer-find-by': 'warn',
      'testing-library/prefer-screen-queries': 'warn',
      'testing-library/render-result-naming-convention': 'warn',
      'testing-library/no-node-access': 'warn',
      'testing-library/no-container': 'warn',
    },
  },
  reactHooks.configs['recommended-latest'],
  {
    name: 'Main ESLint config',
    files: defaultFiles,
    plugins: {
      '@typescript-eslint': eslintTs.plugin,
      filenames: fixupPluginRules(filenames),
    },
    extends: [
      importPlugin.flatConfigs.recommended,
      importPlugin.flatConfigs.typescript,
    ],

    languageOptions: {
      globals: {
        ...globals.browser,
      },
      parser: eslintTs.parser,
      ecmaVersion: 2023,
      sourceType: 'module',
      parserOptions: {
        ...react.configs.flat.recommended.languageOptions.parserOptions,
        jsx: true,
        projectService: true,
        project: './tsconfig.json',
        tsconfigRootDir: __dirname,
      },
    },
    rules: {
      ...importPlugin.configs.typescript.rules,
      // API rules
      '@typescript-eslint/no-explicit-any': 0,
      '@typescript-eslint/explicit-module-boundary-types': 0,
      '@typescript-eslint/no-unused-vars': 0,
      '@typescript-eslint/no-empty-function': 0,
      '@typescript-eslint/no-inferrable-types': 0,
      '@typescript-eslint/no-var-requires': 0,
      '@typescript-eslint/no-unsafe-return': 0,

      // Seems like there is no problem here at all. Very strict
      '@typescript-eslint/no-unsafe-enum-comparison': 0,
      '@typescript-eslint/require-await': 0,

      // Type related warnings and errors we don't want to deal with right now
      '@typescript-eslint/no-unsafe-member-access': 0,
      '@typescript-eslint/restrict-template-expressions': 0,
      '@typescript-eslint/consistent-type-definitions': 0,
      '@typescript-eslint/consistent-type-assertions': 0,
      '@typescript-eslint/consistent-indexed-object-style': 0,
      '@typescript-eslint/consistent-generic-constructor': 0,
      '@typescript-eslint/no-for-in-array': 0,
      '@typescript-eslint/no-unsafe-argument': 0,

      // Should fix these as some point, but disabled to
      '@typescript-eslint/no-unsafe-assignment': 0,
      '@typescript-eslint/no-unsafe-call': 0,
      '@typescript-eslint/unbound-method': 0,

      // This seems to have bugs with declarations https://github.com/microsoft/TypeScript/issues/38347
      '@typescript-eslint/no-base-to-string': 0,

      // Should fix these as some point
      '@typescript-eslint/no-unnecessary-type-assertion': 'warn',
      '@typescript-eslint/no-redundant-type-constituents': 'warn',
      '@typescript-eslint/restrict-plus-operands': 'warn',
      '@typescript-eslint/await-thenable': 'warn',

      '@typescript-eslint/no-floating-promises': 0,
      // '@typescript-eslint/no-floating-promises': ['warn', { ignoreVoid: true }],

      '@typescript-eslint/no-misused-promises': [
        'error',
        {
          checksConditionals: true,
          checksVoidReturn: false,
        },
      ],
      'prefer-const': 'error',
      '@typescript-eslint/ban-types': 0,
      'no-useless-escape': 0,

      'max-params': 'off',
      '@typescript-eslint/max-params': ['warn', { max: 5 }],
      // --- End of copy from API ---

      // We depend on TypeScript to catch these
      'react/prop-types': 0,

      'react/display-name': 0,
      'react/no-unescaped-entities': 0,

      // should fix this
      'import/no-named-as-default-member': 0,
      'import/no-named-as-default': 0,
      'no-case-declarations': 'warn',
      'no-extra-boolean-cast': 'warn',
      'no-prototype-builtins': 'warn',
      'react/jsx-key': 'warn',

      'no-restricted-imports': [
        'error',
        {
          paths: [
            {
              name: 'docx',
              importNames: ['TextRun'],
              message: 'Please import SafeTextRun instead of TextRun from docx.',
            },
            {
              name: 'react-redux',
              importNames: ['useDispatch'],
              message: 'Please use useAppDispatch from "@reducers/index" instead.',
            },
            {
              name: '@apollo/client/utilities',
              importNames: ['cloneDeep'],
              message:
                'Direct usage of `cloneDeep` from `@apollo/client/utilities` is deprecated. Please use `cloneDeep` in `@util/object` instead.',
            },
          ],
        },
      ],

      'import/no-restricted-paths': [
        'warn',
        {
          zones: [
            {
              target: ['./src/atoms/**/*'],
              from: './src/molecules',
              message: 'Atoms should not import molecules.',
            },
            {
              target: ['./src/atoms/**/*', './src/molecules/**/*', './src/features/**/*'],
              from: ['./src/apps', './src/components'],
              message: 'Low level (smaller) components should not import high level components.',
            },
            {
              target: ['./src/apps/company-tracker/**/*'],
              from: ['./src/apps/!(company-tracker)/**/*'],
              message: 'Apps should not import from other apps.',
            },
            {
              target: ['./src/apps/carbon-calculator/**/*'],
              from: ['./src/apps/!(carbon-calculator)/**/*'],
              message: 'Apps should not import from other apps.',
            },
            {
              target: ['./src/apps/portfolio-tracker/**/*'],
              from: ['./src/apps/!(portfolio-tracker)/**/*'],
              message: 'Apps should not import from other apps.',
            },
            {
              target: ['./src/apps/materiality-tracker/**/*'],
              from: ['./src/apps/!(materiality-tracker)/**/*'],
              message: 'Apps should not import from other apps.',
            },
            {
              target: ['./src/apps/assurance/**/*'],
              from: ['./src/apps/!(assurance)/**/*'],
              message: 'Apps should not import from other apps.',
            },
            {
              target: ['./src/features/rich-text-editor/**/*'],
              from: ['./src/features/!(rich-text-editor)/**/*'],
              message: 'Only import what is exposed through feature root exports.',
            }
          ],
        }
      ],

      quotes: ['warn', 'single'],
      'jsx-quotes': ['warn', 'prefer-single'],
      'react-hooks/rules-of-hooks': 'error',
      'react-hooks/exhaustive-deps': 'warn',
      'filenames/match-exported': [1, [null, 'kebab', 'camel']],
      'no-extra-semi': 'warn',
      'import/no-unresolved': 0,
    },
  },
  // other plugins
  ...oxlint.configs['flat/recommended'], // oxlint should be the last one
);


export default configArray;
